xwayland disable

input "type:keyboard" {
	xkb_options caps:ctrl_modifier
}

# output * bg /usr/share/wallpapers/openSUSEdefault/contents/images/default-dark.png fill
output HDMI-A-1 resolution 3840x2160 position 0,0 scale 1.5

# set mod to Super/Windows
set $mod Mod4

# vim like navigation
set $left h
set $down j
set $up k
set $right l

exec command -v autotiling && autotiling

set $term alacritty --config-file ~/.config/alacritty/alacritty.toml
exec "command -v kitty && swaymsg 'set $term kitty'"

set $menu wofi --conf=/etc/wofi/config --style=/etc/wofi/style.css
set $screenlock 'swaylock --config ~/.ilm/suseway/dot-config/swaylock.conf'
set $menu_window $rofi_cmd -show window
set $wob wob --config /etc/sway/wob/wob.ini

exec swaync --style ~/.config/swaync/style.css --config ~/.config/swaync/config.json
exec nm-applet
exec wl-paste -t text --watch clipman store --no-persist

exec_always swaybg -i ~/.config/wallpapers/wallpaper.png -m fill

exec_always {
    systemctl --user import-environment

    gsettings set org.gnome.desktop.interface gtk-theme 'Adwaita-dark'
    gsettings set org.gnome.desktop.interface icon-theme 'Adwaita'
    gsettings set org.gnome.desktop.interface cursor-theme 'Adwaita'
    gsettings set org.gnome.desktop.interface color-scheme 'prefer-dark'

    test -e $SWAYSOCK.wob || mkfifo $SWAYSOCK.wob
    tail -f $SWAYSOCK.wob | $wob
}

exec [ -x /usr/libexec/polkit-gnome-authentication-agent-1 ] && /usr/libexec/polkit-gnome-authentication-agent-1
# exec /usr/libexec/polkit-gnome-authentication-agent-1
#exec dbus-update-activation-environment --systemd WAYLAND_DISPLAY XDG_CURRENT_DESKTOP

exec killall swayidle
exec_always swayidle -w \
    timeout 60 'swaymsg "output * power off"' \
    resume 'swaymsg "output * power on"' \
    before-sleep '$screenlock' \
    timeout 600 'systemctl suspend'

seat * xcursor_theme Adwaita 32

default_border pixel 4
default_floating_border pixel 4

gaps outer 5
gaps inner 10

# Format: client.<class> <border> <background> <text> <indicator> [<child_border>]

# Example with all 5 values:
client.focused          #6da741 #173f4f #73ba25 #39ff14 #6da741
client.unfocused        #00a489 #173f4f #35b9ab #484e50 #173f4f
client.focused_inactive #6da741 #00a489 #173f4f #484e50 #00a489

# # client.focused #6da741 173f4f #73ba25
# # client.focused #39ff14 #173f4f #73ba25
# client.unfocused #00a489 #173f4f #35b9ab
# client.focused_inactive #6da741 #00a489 #173f4f

bar {
    swaybar_command waybar
}

bindsym $mod+Return exec $$term
bindsym $mod+Shift+q kill
bindsym $mod+d exec $menu
floating_modifier $mod normal

bindsym $mod+Shift+c reload

bindsym $mod+Shift+e exec swaynag -t warning -m 'You pressed the exit shortcut. Do you really want to exit sway? This will end your Wayland session.' -b 'Yes, exit sway' 'swaymsg exit'

bindsym $mod+$left focus left
bindsym $mod+$down focus down
bindsym $mod+$up focus up
bindsym $mod+$right focus right

bindsym $mod+Left focus left
bindsym $mod+Down focus down
bindsym $mod+Up focus up
bindsym $mod+Right focus right

bindsym $mod+Shift+$left move left
bindsym $mod+Shift+$down move down
bindsym $mod+Shift+$up move up
bindsym $mod+Shift+$right move right

bindsym $mod+Shift+Left move left
bindsym $mod+Shift+Down move down
bindsym $mod+Shift+Up move up
bindsym $mod+Shift+Right move right

bindsym $mod+1 workspace number 1
bindsym $mod+2 workspace number 2
bindsym $mod+3 workspace number 3
bindsym $mod+4 workspace number 4
bindsym $mod+5 workspace number 5
bindsym $mod+6 workspace number 6
bindsym $mod+7 workspace number 7
bindsym $mod+8 workspace number 8
bindsym $mod+9 workspace number 9
bindsym $mod+0 workspace number 10

bindsym $mod+Shift+1 move container to workspace number 1
bindsym $mod+Shift+2 move container to workspace number 2
bindsym $mod+Shift+3 move container to workspace number 3
bindsym $mod+Shift+4 move container to workspace number 4
bindsym $mod+Shift+5 move container to workspace number 5
bindsym $mod+Shift+6 move container to workspace number 6
bindsym $mod+Shift+7 move container to workspace number 7
bindsym $mod+Shift+8 move container to workspace number 8
bindsym $mod+Shift+9 move container to workspace number 9
bindsym $mod+Shift+0 move container to workspace number 10

bindsym $mod+b splith
bindsym $mod+v splitv

bindsym $mod+s layout stacking
bindsym $mod+w layout tabbed
bindsym $mod+e layout toggle split

bindsym $mod+f fullscreen

bindsym $mod+Shift+space floating toggle

bindsym $mod+space focus mode_toggle

bindsym $mod+a focus parent

bindsym $mod+Shift+minus move scratchpad
bindsym $mod+minus scratchpad show

mode "resize" {
    bindsym $left resize shrink width 10px
    bindsym $down resize grow height 10px
    bindsym $up resize shrink height 10px
    bindsym $right resize grow width 10px

    bindsym Left resize shrink width 10px
    bindsym Down resize grow height 10px
    bindsym Up resize shrink height 10px
    bindsym Right resize grow width 10px

    bindsym Return mode "default"
    bindsym Escape mode "default"
}
bindsym $mod+r mode "resize"

# input "type:touchpad" {
#   tap enabled
#   natural_scroll enabled
#   middle_emulation enabled
# }

bindsym $mod+tab workspace next_on_output
bindsym $mod+Shift+tab workspace prev_on_output

# bindsym --to-code {
#     $mod+b splith
#     $mod+v splitv
# }

# Media keys
bindsym XF86AudioMicMute exec pactl set-source-mute @DEFAULT_SOURCE@ toggle

bindsym XF86MonBrightnessDown exec brightnessctl -q set 5%- && ( echo $((`brightnessctl get` * 100 / `brightnessctl m`)) > $SWAYSOCK.wob )
bindsym XF86MonBrightnessUp exec brightnessctl -q set +5% && ( echo $((`brightnessctl get` * 100 / `brightnessctl m`)) > $SWAYSOCK.wob )

bindsym XF86AudioRaiseVolume exec pamixer --allow-boost -ui 2 && dc -e "[`pamixer --get-volume`]sM 100d `pamixer --get-volume`<Mp" > $SWAYSOCK.wob
bindsym XF86AudioLowerVolume exec pamixer --allow-boost -ud 2 && dc -e "[`pamixer --get-volume`]sM 100d `pamixer --get-volume`<Mp" > $SWAYSOCK.wob
bindsym XF86AudioMute exec pamixer --toggle-mute && ( pamixer --get-mute && echo 0 > $SWAYSOCK.wob )

# Media player controls
bindsym --no-warn XF86AudioPlay exec playerctl play-pause
bindsym --no-warn XF86AudioNext exec playerctl next
bindsym --no-warn XF86AudioPrev exec playerctl previous

# Screenshots
set $selected_window swaymsg -t get_tree | jq -r '.. | select(.pid? and .visible?) | .rect | "\(.x),\(.y) \(.width)x\(.height)"' | slurp
set $focused_window swaymsg -t get_tree | jq -j '.. | select(.type?) | select(.focused).rect | "\(.x),\(.y) \(.width)x\(.height)"'
set $focused_output swaymsg -t get_outputs | jq -r '.[] | select(.focused) | .name'

## Screenshot commands
### Full
set $screenshot_full grim
set $screenshot_full_clipboard grim - | wl-copy
### Selected window
set $screenshot_selected_window $selected_window | grim -g-
set $screenshot_selected_window_clipboard $selected_window | grim -g- - | wl-copy
### Selected area
set $screenshot_selected_area slurp | grim -g-
set $screenshot_selected_area_clipboard slurp | grim -g- - | wl-copy
### Focused window
set $screenshot_focused_window $focused_window | grim -g-
set $screenshot_focused_window_clipboard $focused_window | grim -g- - | wl-copy
### Focused output
set $screenshot_focused_output grim -o $($focused_output)
set $screenshot_focused_output_clipboard grim -o $($focused_output) - | wl-copy

## Screenshot mode menu
set $screenshot "Screenshot: (f) full, (s) select window, (a) select area, (w) focused window, (o) focused output [Ctrl+ saves to clipboard]"
mode $screenshot {
    # Full
    bindsym f exec $screenshot_full; mode "default"
    bindsym Ctrl+f exec $screenshot_full_clipboard; mode "default"
    # Selected window
    bindsym s exec $screenshot_selected_window; mode "default"
    bindsym Ctrl+s exec $screenshot_selected_window_clipboard; mode "default"
    # Selected area
    bindsym a exec $screenshot_selected_area; mode "default"
    bindsym Ctrl+a exec $screenshot_selected_area_clipboard; mode "default"
    # Focused window
    bindsym w exec $screenshot_focused_window; mode "default"
    bindsym Ctrl+w exec $screenshot_focused_window_clipboard; mode "default"
    # Focused output
    bindsym o exec $screenshot_focused_output; mode "default"
    bindsym Ctrl+o exec $screenshot_focused_output_clipboard; mode "default"

    # Exit screenshot mode menu
    bindsym Return mode "default"
    bindsym Escape mode "default"
    bindsym $mod+Print mode "default"
}
bindsym $mod+Print mode $screenshot

# System mode menu
set $mode_system "What to do? (l) lock, (e) logout, (r) reboot, (s) suspend, (Shift+s) shutdown"
mode $mode_system {
	bindsym l exec $screenlock; mode "default"
	bindsym e exec swaymsg exit; mode "default"
	bindsym r exec systemctl reboot; mode "default"
	bindsym s exec systemctl suspend; mode "default"
	bindsym Shift+s exec systemctl poweroff; mode "default"

	# back to normal: Enter or Escape
	bindsym Return mode "default"
	bindsym Escape mode "default"
}
unbindsym $mod+Shift+e
bindsym $mod+Shift+e mode $mode_system

# Toggle notification bar
set $toggle_notification "swaync-client -t -sw"
bindsym $mod+Shift+n exec $toggle_notification

# Wayland default floats
for_window [app_id="(Calendar|Calculator|Power-statistics|Control-center)"] floating enable
for_window [app_id="org.gnome.(Nautilus|Weather)"] floating enable
for_window [app_id="nm-connection-editor"] floating enable
for_window [app_id="(pavucontrol|psensor)"] floating enable
for_window [app_id="evolution-alarm-notify"] floating enable
for_window [app_id="nemo"] floating enable
for_window [app_id="evolution" title="Compose Message"] floating enable
for_window [app_id="evolution" title="Re(.*)"] floating enable
for_window [app_id="evolution" title="Fwd(.*)"] floating enable
for_window [app_id="mpv"] floating enable
for_window [app_id="mpv"] resize set 960 540
for_window [app_id="mpv"] border pixel 0
for_window [app_id="firefox" title="Pushbullet"] floating enable
for_window [app_id="firefox" title="Firefox — Sharing Indicator"] floating enable
for_window [app_id="firefox" title="Picture-in-Picture"] floating enable
for_window [app_id="firefox" title="Pushbullet – Mozilla Firefox"] floating enable
for_window [app_id="firefox" title="About Mozilla Firefox"] floating enable

# Brave, chrome, chromium
for_window [title="Picture-in-picture"] floating enable
