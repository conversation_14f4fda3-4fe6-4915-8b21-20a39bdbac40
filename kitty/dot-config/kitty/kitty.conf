#    __ ___ __  __
#   / //_(_) /_/ /___ __
#  / ,< / / __/ __/ // /
# /_/|_/_/\__/\__/\_, /
#                /___/
#
# Configuration
font_family                 JetbrainsMono Nerd Font
font_size	                  13
bold_font                   auto
italic_font                 auto
bold_italic_font            auto
remember_window_size        no
initial_window_width        950
initial_window_height       500
cursor_blink_interval       0.5
cursor_stop_blinking_after  1
scrollback_lines            2000
wheel_scroll_min_lines      1
enable_audio_bell           no
window_padding_width        10
# hide_window_decorations     yes
background_opacity          0.9
dynamic_background_opacity  yes
confirm_os_window_close     0
selection_foreground        none
selection_background        none

# vim:ft=kitty

## name:     Cat<PERSON>uccin Kitty Diff Mocha
## author:   Catppuccin Org
## license:  MIT
## upstream: https://github.com/catppuccin/kitty/blob/main/themes/diff-mocha.conf
## blurb:    Soothing pastel theme for the high-spirited!

# text
foreground           #cdd6f4
# base
background           #1e1e2e
# subtext0
title_fg             #a6adc8

# mantle
title_bg             #181825
margin_bg            #181825

# subtext1
margin_fg            #a6adc8
# mantle
filler_bg            #181825

# 30% red, 70% base
removed_bg           #5e3f53
# 50% red, 50% base
highlight_removed_bg #89556b
# 40% red, 60% base
removed_margin_bg    #734a5f

# 30% green, 70% base
added_bg             #475a51
# 50% green, 50% base
highlight_added_bg   #628168
# 40% green, 60% base
added_margin_bg      #734a5f

# mantle
hunk_margin_bg       #181825
hunk_bg              #181825

# 40% yellow, 60% base
search_bg            #766c62
# text
search_fg            #cdd6f4
# 30% sky, 70% base
select_bg            #3e5767
# text
select_fg            #cdd6f4
