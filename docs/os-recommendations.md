# OS recommendations

1. If you need Ubuntu, use [kubuntu](https://kubuntu.org/feature-tour)

2. If you want Gnome, use [Bluefin](https://projectbluefin.io) or [Fedora Workstation](https://fedoraproject.org/workstation/)

3. If you want KDE, use [Tumbleweed](https://get.opensuse.org/tumbleweed) or [Aurora](https://getaurora.dev/en)

4. If you want a tiling window manager, use [Fedora Sway](https://fedoraproject.org/spins/sway) or [Suseway](https://get.opensuse.org/tumbleweed)] or [hyprland](https://hyprland.org/)

5. If you want to run linux desktop environment in a virtual machine, then [kubuntu](https://kubuntu.org/feature-tour) is more stable.

6. For containers prefer debian or ubuntu.

7. For development only containers, you could use fedora and tumbleweed, especially if you don't want to use `brew` or `nix`.

7. Give [Nix](https://nixos.org) a try.
