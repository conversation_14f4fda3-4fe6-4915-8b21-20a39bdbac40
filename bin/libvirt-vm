#!/bin/bash

set -euo pipefail

RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

log_info() { echo -e "${BLUE}[INFO]${NC} $*"; }
log_success() { echo -e "${GREEN}[SUCCESS]${NC} $*"; }
log_warn() { echo -e "${YELLOW}[WARN]${NC} $*"; }
log_error() { echo -e "${RED}[ERROR]${NC} $*" >&2; }

usage() {
    cat <<EOF
Usage: $0 <command> [vm-name]

Manage Docker VMs created with create-docker-vm script.

COMMANDS:
    list                List all VMs
    status <vm-name>    Show VM status and info
    start <vm-name>     Start a VM
    stop <vm-name>      Gracefully stop a VM
    restart <vm-name>   Restart a VM
    destroy <vm-name>   Force stop a VM
    delete <vm-name>    Delete a VM completely
    console <vm-name>   Connect to VM console
    ssh <vm-name>       SSH into VM (shows command)
    ip <vm-name>        Get VM IP address
    logs <vm-name>      Show VM logs
    cleanup             Remove stopped VMs and orphaned files

EXAMPLES:
    $0 list                    # List all VMs
    $0 status docker           # Show status of 'docker' VM
    $0 start docker            # Start 'docker' VM
    $0 ssh docker              # Get SSH command for 'docker' VM
    $0 delete old-vm           # Delete 'old-vm' completely

EOF
}

list_vms() {
    log_info "Listing all VMs..."
    echo
    virsh list --all
}

vm_status() {
    local vm_name="$1"

    if ! virsh dominfo "$vm_name" &>/dev/null; then
        log_error "VM '$vm_name' not found"
        return 1
    fi

    log_info "Status for VM '$vm_name':"
    echo
    virsh dominfo "$vm_name"
    echo

    log_info "Network interfaces:"
    virsh domifaddr "$vm_name" || log_warn "Could not get IP address (VM may be stopped)"
    echo

    log_info "Disk usage:"
    virsh domblklist "$vm_name"
}

start_vm() {
    local vm_name="$1"

    if ! virsh dominfo "$vm_name" &>/dev/null; then
        log_error "VM '$vm_name' not found"
        return 1
    fi

    local state
    state=$(virsh domstate "$vm_name")

    if [[ "$state" == "running" ]]; then
        log_warn "VM '$vm_name' is already running"
        return 0
    fi

    log_info "Starting VM '$vm_name'..."
    if virsh start "$vm_name"; then
        log_success "VM '$vm_name' started"
        sleep 2
        log_info "Waiting for network..."
        sleep 3
        get_vm_ip "$vm_name"
    else
        log_error "Failed to start VM '$vm_name'"
        return 1
    fi
}

stop_vm() {
    local vm_name="$1"

    if ! virsh dominfo "$vm_name" &>/dev/null; then
        log_error "VM '$vm_name' not found"
        return 1
    fi

    local state
    state=$(virsh domstate "$vm_name")

    if [[ "$state" != "running" ]]; then
        log_warn "VM '$vm_name' is not running"
        return 0
    fi

    log_info "Gracefully stopping VM '$vm_name'..."
    if virsh shutdown "$vm_name"; then
        log_success "Shutdown command sent to VM '$vm_name'"
        log_info "Waiting for VM to stop..."

        # Wait up to 60 seconds for graceful shutdown
        local count=0
        while [[ $count -lt 20 ]]; do
            state=$(virsh domstate "$vm_name")
            if [[ "$state" == "shut off" ]]; then
                log_success "VM '$vm_name' stopped gracefully"
                return 0
            fi
            sleep 3
            ((count++))
        done

        log_warn "VM didn't stop gracefully, forcing shutdown..."
        virsh destroy "$vm_name"
        log_success "VM '$vm_name' force stopped"
    else
        log_error "Failed to stop VM '$vm_name'"
        return 1
    fi
}

restart_vm() {
    local vm_name="$1"

    log_info "Restarting VM '$vm_name'..."
    stop_vm "$vm_name"
    sleep 2
    start_vm "$vm_name"
}

destroy_vm() {
    local vm_name="$1"

    if ! virsh dominfo "$vm_name" &>/dev/null; then
        log_error "VM '$vm_name' not found"
        return 1
    fi

    log_warn "Force stopping VM '$vm_name'..."
    if virsh destroy "$vm_name"; then
        log_success "VM '$vm_name' force stopped"
    else
        log_warn "VM '$vm_name' was not running or already stopped"
    fi
}

delete_vm() {
    local vm_name="$1"

    if ! virsh dominfo "$vm_name" &>/dev/null; then
        log_error "VM '$vm_name' not found"
        return 1
    fi

    log_warn "This will permanently delete VM '$vm_name' and all its data!"
    read -p "Are you sure? (y/N): " -r
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        log_info "Deletion cancelled"
        return 0
    fi

    local state
    state=$(virsh domstate "$vm_name")
    if [[ "$state" == "running" ]]; then
        log_info "Stopping VM first..."
        virsh destroy "$vm_name"
    fi

    local disks
    disks=$(virsh domblklist "$vm_name" --details | awk '/file.*disk/ {print $4}')

    log_info "Removing VM definition..."
    if virsh undefine "$vm_name"; then
        log_success "VM '$vm_name' undefined"
    fi

    if [[ -n "$disks" ]]; then
        log_info "Removing disk files..."
        while IFS= read -r disk; do
            if [[ -f "$disk" ]]; then
                log_info "Removing disk: $disk"
                rm -f "$disk"
            fi
        done <<<"$disks"
    fi

    local workdir="$HOME/${vm_name}-vm"
    if [[ -d "$workdir" ]]; then
        log_info "Removing working directory: $workdir"
        rm -rf "$workdir"
    fi

    log_success "VM '$vm_name' completely deleted"
}

connect_console() {
    local vm_name="$1"

    if ! virsh dominfo "$vm_name" &>/dev/null; then
        log_error "VM '$vm_name' not found"
        return 1
    fi

    local state
    state=$(virsh domstate "$vm_name")

    if [[ "$state" != "running" ]]; then
        log_error "VM '$vm_name' is not running"
        log_info "Start it with: $0 start $vm_name"
        return 1
    fi

    log_info "Connecting to console of VM '$vm_name'..."
    log_info "Press Ctrl+] to exit console"
    echo
    virsh console "$vm_name"
}

get_vm_ip() {
    local vm_name="$1"

    if ! virsh dominfo "$vm_name" &>/dev/null; then
        log_error "VM '$vm_name' not found"
        return 1
    fi

    local state
    state=$(virsh domstate "$vm_name")

    if [[ "$state" != "running" ]]; then
        log_error "VM '$vm_name' is not running"
        return 1
    fi

    log_info "Getting IP address for VM '$vm_name'..."

    local ip_info
    ip_info=$(virsh domifaddr "$vm_name" 2>/dev/null)

    if [[ -z "$ip_info" ]] || ! echo "$ip_info" | grep -q "ipv4"; then
        log_warn "No IP address found. VM may still be booting."
        log_info "Try again in a few seconds, or check with: virsh domifaddr $vm_name"
        return 1
    fi

    echo "$ip_info"

    local ip
    ip=$(echo "$ip_info" | awk '/ipv4/ {print $4}' | cut -d'/' -f1 | head -1)

    if [[ -n "$ip" ]]; then
        echo
        log_success "VM IP: $ip"
        log_info "SSH command: ssh ubuntu@$ip"
    fi
}

show_ssh_command() {
    local vm_name="$1"
    get_vm_ip "$vm_name"
}

show_logs() {
    local vm_name="$1"

    if ! virsh dominfo "$vm_name" &>/dev/null; then
        log_error "VM '$vm_name' not found"
        return 1
    fi

    log_info "Showing logs for VM '$vm_name'..."
    echo

    local log_file="/var/log/libvirt/qemu/${vm_name}.log"
    if [[ -f "$log_file" ]]; then
        log_info "Libvirt logs:"
        sudo tail -20 "$log_file"
    else
        log_warn "No libvirt log file found at $log_file"
    fi
}

cleanup_vms() {
    log_info "Cleaning up stopped VMs and orphaned files..."

    # List stopped VMs
    local stopped_vms
    stopped_vms=$(virsh list --inactive --name)

    if [[ -n "$stopped_vms" ]]; then
        log_info "Stopped VMs found:"
        echo "$stopped_vms"
        echo

        read -p "Remove all stopped VMs? (y/N): " -r
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            while IFS= read -r vm; do
                if [[ -n "$vm" ]]; then
                    log_info "Removing stopped VM: $vm"
                    delete_vm "$vm" </dev/null
                fi
            done <<<"$stopped_vms"
        fi
    else
        log_info "No stopped VMs found"
    fi

    log_info "Checking for orphaned working directories..."
    for dir in "$HOME"/*-vm; do
        if [[ -d "$dir" ]]; then
            local vm_name
            vm_name=$(basename "$dir" | sed 's/-vm$//')
            if ! virsh dominfo "$vm_name" &>/dev/null; then
                log_warn "Found orphaned directory: $dir"
                read -p "Remove orphaned directory $dir? (y/N): " -r
                if [[ $REPLY =~ ^[Yy]$ ]]; then
                    rm -rf "$dir"
                    log_success "Removed: $dir"
                fi
            fi
        fi
    done

    log_success "Cleanup complete"
}

if [[ $# -eq 0 ]]; then
    usage
    exit 1
fi

command="$1"
vm_name="${2:-}"

case "$command" in
list)
    list_vms
    ;;
status)
    [[ -z "$vm_name" ]] && {
        log_error "VM name required"
        usage
        exit 1
    }
    vm_status "$vm_name"
    ;;
start)
    [[ -z "$vm_name" ]] && {
        log_error "VM name required"
        usage
        exit 1
    }
    start_vm "$vm_name"
    ;;
stop)
    [[ -z "$vm_name" ]] && {
        log_error "VM name required"
        usage
        exit 1
    }
    stop_vm "$vm_name"
    ;;
restart)
    [[ -z "$vm_name" ]] && {
        log_error "VM name required"
        usage
        exit 1
    }
    restart_vm "$vm_name"
    ;;
destroy)
    [[ -z "$vm_name" ]] && {
        log_error "VM name required"
        usage
        exit 1
    }
    destroy_vm "$vm_name"
    ;;
delete)
    [[ -z "$vm_name" ]] && {
        log_error "VM name required"
        usage
        exit 1
    }
    delete_vm "$vm_name"
    ;;
console)
    [[ -z "$vm_name" ]] && {
        log_error "VM name required"
        usage
        exit 1
    }
    connect_console "$vm_name"
    ;;
ssh)
    [[ -z "$vm_name" ]] && {
        log_error "VM name required"
        usage
        exit 1
    }
    show_ssh_command "$vm_name"
    ;;
ip)
    [[ -z "$vm_name" ]] && {
        log_error "VM name required"
        usage
        exit 1
    }
    get_vm_ip "$vm_name"
    ;;
logs)
    [[ -z "$vm_name" ]] && {
        log_error "VM name required"
        usage
        exit 1
    }
    show_logs "$vm_name"
    ;;
cleanup)
    cleanup_vms
    ;;
--help | -h)
    usage
    ;;
*)
    log_error "Unknown command: $command"
    usage
    exit 1
    ;;
esac
