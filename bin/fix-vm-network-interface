#!/bin/bash

# Fix VM Network Interface Names
# Updates all VM creation scripts to use the correct network interface

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() { echo -e "${BLUE}[INFO]${NC} $*"; }
log_success() { echo -e "${GREEN}[SUCCESS]${NC} $*"; }
log_warn() { echo -e "${YELLOW}[WARN]${NC} $*"; }
log_error() { echo -e "${RED}[ERROR]${NC} $*" >&2; }

# Detect the correct network interface
detect_interface() {
    local interface

    # Try to find the primary network interface
    interface=$(ip route | grep default | awk '{print $5}' | head -1)

    if [[ -n "$interface" ]]; then
        echo "$interface"
        return 0
    fi

    # Fallback: look for common interface patterns
    for iface in enp2s0 enp1s0 eth0 ens3 ens18; do
        if ip link show "$iface" &>/dev/null; then
            echo "$iface"
            return 0
        fi
    done

    return 1
}

fix_vm_script() {
    local script_file="$1"
    local old_interface="$2"
    local new_interface="$3"

    if [[ ! -f "$script_file" ]]; then
        log_warn "Script not found: $script_file"
        return 1
    fi

    log_info "Fixing $script_file: $old_interface -> $new_interface"

    # Create backup
    cp "$script_file" "${script_file}.backup"

    # Replace the interface name
    sed -i "s/$old_interface/$new_interface/g" "$script_file"

    log_success "Fixed $script_file"
}

usage() {
    cat <<EOF
Usage: $0 <interface_name>

Fix VM network interface names in all VM creation scripts.

PARAMETERS:
    interface_name    Network interface name (e.g., enp2s0, eth0, ens3)

EXAMPLES:
    $0 enp2s0         # Use enp2s0 interface
    $0 eth0           # Use eth0 interface
    $0 ens3           # Use ens3 interface

This script will:
1. Update all VM creation scripts to use the specified interface
2. Create backups of original scripts (.backup extension)
3. Show which scripts were modified

VM Scripts that will be updated:
- bin/create-arch-vm
- bin/create-docker-vm
- bin/create-fedora-vm
- bin/create-ubuntu-vm
- bin/create-nixos-vm

EOF
}

main() {
    local target_interface="$1"

    log_info "Fixing VM network interface names to use: $target_interface"

    # Validate the interface exists
    if ! ip link show "$target_interface" &>/dev/null; then
        log_warn "Interface '$target_interface' not found on this system"
        log_info "Available interfaces:"
        ip link show | grep -E "^[0-9]+:" | awk '{print $2}' | sed 's/:$//' | grep -v lo
        echo
        read -p "Continue anyway? (y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            log_info "Aborted"
            exit 0
        fi
    fi

    # List of VM scripts to fix
    local vm_scripts=(
        "bin/create-arch-vm"
        "bin/create-docker-vm"
        "bin/create-fedora-vm"
        "bin/create-ubuntu-vm"
        "bin/create-nixos-vm"
    )

    local old_interface="enp1s0"
    local fixed_count=0

    log_info "Updating scripts from '$old_interface' to '$target_interface'..."
    echo

    for script in "${vm_scripts[@]}"; do
        if [[ -f "$script" ]]; then
            if grep -q "$old_interface" "$script"; then
                fix_vm_script "$script" "$old_interface" "$target_interface"
                ((fixed_count++))
            else
                log_info "$script: No changes needed (already correct or no interface specified)"
            fi
        else
            log_warn "$script: File not found"
        fi
    done

    echo
    if [[ $fixed_count -gt 0 ]]; then
        log_success "Successfully fixed $fixed_count VM scripts"
        log_info "Backups created with .backup extension"
        echo
        log_info "Next steps:"
        echo "1. Restart any running VMs to pick up network changes:"
        echo "   virsh shutdown <vm-name> && virsh start <vm-name>"
        echo
        echo "2. Or recreate VMs that don't have network connectivity"
        echo
        echo "3. Test with: virsh domifaddr <vm-name>"
    else
        log_info "No scripts needed fixing"
    fi
}

# Check if interface parameter is provided
if [[ $# -eq 0 ]]; then
    log_error "Missing required parameter: interface_name"
    echo
    usage
    exit 1
fi

if [[ "$1" == "--help" || "$1" == "-h" ]]; then
    usage
    exit 0
fi

main "$@"
