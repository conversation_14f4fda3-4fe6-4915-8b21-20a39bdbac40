#!/bin/bash

# Fix VM Network Interface Names
# Updates all VM creation scripts to use the correct network interface

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() { echo -e "${BLUE}[INFO]${NC} $*"; }
log_success() { echo -e "${GREEN}[SUCCESS]${NC} $*"; }
log_warn() { echo -e "${YELLOW}[WARN]${NC} $*"; }
log_error() { echo -e "${RED}[ERROR]${NC} $*" >&2; }

# Detect the correct network interface
detect_interface() {
    local interface
    
    # Try to find the primary network interface
    interface=$(ip route | grep default | awk '{print $5}' | head -1)
    
    if [[ -n "$interface" ]]; then
        echo "$interface"
        return 0
    fi
    
    # Fallback: look for common interface patterns
    for iface in enp2s0 enp1s0 eth0 ens3 ens18; do
        if ip link show "$iface" &>/dev/null; then
            echo "$iface"
            return 0
        fi
    done
    
    return 1
}

fix_vm_script() {
    local script_file="$1"
    local old_interface="$2"
    local new_interface="$3"
    
    if [[ ! -f "$script_file" ]]; then
        log_warn "Script not found: $script_file"
        return 1
    fi
    
    log_info "Fixing $script_file: $old_interface -> $new_interface"
    
    # Create backup
    cp "$script_file" "${script_file}.backup"
    
    # Replace the interface name
    sed -i "s/$old_interface/$new_interface/g" "$script_file"
    
    log_success "Fixed $script_file"
}

main() {
    log_info "Fixing VM network interface names..."
    
    # Detect the correct interface
    local correct_interface
    if ! correct_interface=$(detect_interface); then
        log_error "Could not detect network interface"
        log_error "Please specify manually: $0 <interface_name>"
        exit 1
    fi
    
    log_success "Detected network interface: $correct_interface"
    
    # List of VM scripts to fix
    local vm_scripts=(
        "bin/create-arch-vm"
        "bin/create-docker-vm"
        "bin/create-fedora-vm"
        "bin/create-ubuntu-vm"
        "bin/create-nixos-vm"
    )
    
    local old_interface="enp1s0"
    local fixed_count=0
    
    for script in "${vm_scripts[@]}"; do
        if [[ -f "$script" ]]; then
            if grep -q "$old_interface" "$script"; then
                fix_vm_script "$script" "$old_interface" "$correct_interface"
                ((fixed_count++))
            else
                log_info "$script already uses correct interface or doesn't specify one"
            fi
        else
            log_warn "Script not found: $script"
        fi
    done
    
    if [[ $fixed_count -gt 0 ]]; then
        log_success "Fixed $fixed_count VM scripts"
        log_info "Backups created with .backup extension"
        echo
        log_info "You should now restart any running VMs to pick up the network changes:"
        echo "  virsh shutdown <vm-name> && virsh start <vm-name>"
        echo
        log_info "Or recreate VMs that don't have network connectivity"
    else
        log_info "No scripts needed fixing"
    fi
}

# Allow manual interface specification
if [[ $# -eq 1 ]]; then
    MANUAL_INTERFACE="$1"
    log_info "Using manually specified interface: $MANUAL_INTERFACE"
    
    # Override the detect function
    detect_interface() {
        echo "$MANUAL_INTERFACE"
    }
fi

main "$@"
