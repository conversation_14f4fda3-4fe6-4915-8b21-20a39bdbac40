#!/bin/bash

# Enhanced NixOS VM Creation Script
# Creates NixOS VMs using cloud-init and virt-install
#
# Features:
# - Automatic NixOS cloud image download
# - SSH key setup for passwordless access
# - Configurable VM resources and networking
# - Robust error handling and cleanup
# - VM management utilities

set -euo pipefail

# === Default Configuration ===
VM_NAME="nixos"
RAM_MB=4096
VCPUS=2
DISK_SIZE="40G"
SSH_KEY="${HOME}/.ssh/id_rsa.pub"
NIXOS_RELEASE="24.11"
NIXOS_IMG_URL="https://channels.nixos.org/nixos-${NIXOS_RELEASE}/latest-nixos-minimal-x86_64-linux.iso"
WORKDIR="$HOME/${VM_NAME}-vm"
CLOUD_INIT_DIR="${WORKDIR}/cloud-init"
BASE_IMG="${WORKDIR}/nixos-${NIXOS_RELEASE}.iso"
DISK_IMG="${WORKDIR}/${VM_NAME}.qcow2"
SEED_ISO="${CLOUD_INIT_DIR}/seed.iso"
BRIDGE_IF="virbr0"
USERNAME="nixos"
AUTO_INSTALL=false

# === Color output ===
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# === Utility Functions ===
log_info() { echo -e "${BLUE}[INFO]${NC} $*"; }
log_success() { echo -e "${GREEN}[SUCCESS]${NC} $*"; }
log_warn() { echo -e "${YELLOW}[WARN]${NC} $*"; }
log_error() { echo -e "${RED}[ERROR]${NC} $*" >&2; }

cleanup_on_error() {
    log_warn "Cleaning up due to error..."
    [[ -f "$DISK_IMG" ]] && rm -f "$DISK_IMG"
    [[ -f "$SEED_ISO" ]] && rm -f "$SEED_ISO"
    virsh destroy "$VM_NAME" 2>/dev/null || true
    virsh undefine "$VM_NAME" 2>/dev/null || true
}

trap cleanup_on_error ERR

usage() {
    cat <<EOF
Usage: $0 [OPTIONS]

Create a NixOS VM using virt-install and cloud-init.

OPTIONS:
    --name NAME         VM name (default: $VM_NAME)
    --memory MB         RAM in MB (default: $RAM_MB)
    --vcpus NUM         Number of vCPUs (default: $VCPUS)
    --disk-size SIZE    Disk size (default: $DISK_SIZE)
    --ssh-key PATH      SSH public key path (default: $SSH_KEY)
    --bridge BRIDGE     Network bridge (default: $BRIDGE_IF)
    --nixos-release REL NixOS release (default: $NIXOS_RELEASE)
    --workdir DIR       Working directory (default: $WORKDIR)
    --username USER     VM username (default: $USERNAME)
    --auto-install      Automatically install NixOS after VM creation
    --help, -h          Show this help

EXAMPLES:
    $0                                    # Create VM with defaults
    $0 --name myvm --memory 8192          # Custom name and memory
    $0 --disk-size 80G --vcpus 4          # Larger disk and more CPUs
    $0 --nixos-release 24.05              # Use NixOS 24.05
    $0 --auto-install                     # Create VM and auto-install NixOS
    $0 --name myvm --auto-install         # Custom name with auto-install

PREREQUISITES:
    - KVM/QEMU installed and running
    - libvirt daemon running
    - virt-install package installed
    - SSH key pair generated
    - Network bridge configured (or use default virbr0)

NOTE: NixOS uses ISO installation method rather than cloud images.
The VM will boot from ISO and require manual installation or
use of automated installation scripts.

For fully automated installation, use the --auto-install flag.
This requires the 'expect' package to be installed.

EOF
}

# === Argument Parsing ===
while [[ $# -gt 0 ]]; do
    case $1 in
    --name)
        VM_NAME="$2"
        WORKDIR="$HOME/${VM_NAME}-vm"
        CLOUD_INIT_DIR="${WORKDIR}/cloud-init"
        DISK_IMG="${WORKDIR}/${VM_NAME}.qcow2"
        SEED_ISO="${CLOUD_INIT_DIR}/seed.iso"
        shift 2
        ;;
    --memory)
        RAM_MB="$2"
        shift 2
        ;;
    --vcpus)
        VCPUS="$2"
        shift 2
        ;;
    --disk-size)
        DISK_SIZE="$2"
        shift 2
        ;;
    --ssh-key)
        SSH_KEY="$2"
        shift 2
        ;;
    --bridge)
        BRIDGE_IF="$2"
        shift 2
        ;;
    --nixos-release)
        NIXOS_RELEASE="$2"
        NIXOS_IMG_URL="https://channels.nixos.org/nixos-${NIXOS_RELEASE}/latest-nixos-minimal-x86_64-linux.iso"
        BASE_IMG="${WORKDIR}/nixos-${NIXOS_RELEASE}.iso"
        shift 2
        ;;
    --workdir)
        WORKDIR="$2"
        CLOUD_INIT_DIR="${WORKDIR}/cloud-init"
        BASE_IMG="${WORKDIR}/nixos-${NIXOS_RELEASE}.iso"
        DISK_IMG="${WORKDIR}/${VM_NAME}.qcow2"
        SEED_ISO="${CLOUD_INIT_DIR}/seed.iso"
        shift 2
        ;;
    --username)
        USERNAME="$2"
        shift 2
        ;;
    --auto-install)
        AUTO_INSTALL=true
        shift
        ;;
    --help | -h)
        usage
        exit 0
        ;;
    *)
        log_error "Unknown option: $1"
        usage
        exit 1
        ;;
    esac
done

# === Validation Functions ===
check_prerequisites() {
    log_info "Checking prerequisites..."

    local missing_tools=()
    local required_tools=(virsh virt-install qemu-img wget mkisofs)

    # Add expect to required tools if auto-install is enabled
    if [[ "$AUTO_INSTALL" == "true" ]]; then
        required_tools+=(expect)
    fi

    for tool in "${required_tools[@]}"; do
        if ! command -v "$tool" &>/dev/null; then
            missing_tools+=("$tool")
        fi
    done

    if [[ ${#missing_tools[@]} -gt 0 ]]; then
        log_error "Missing required tools: ${missing_tools[*]}"
        if [[ "$AUTO_INSTALL" == "true" ]]; then
            log_error "Install with: sudo apt install qemu-kvm libvirt-daemon-system libvirt-clients virtinst qemu-utils wget genisoimage expect"
        else
            log_error "Install with: sudo apt install qemu-kvm libvirt-daemon-system libvirt-clients virtinst qemu-utils wget genisoimage"
        fi
        exit 1
    fi

    # Check if libvirtd is running
    if ! systemctl is-active --quiet libvirtd; then
        log_error "libvirtd service is not running"
        log_error "Start with: sudo systemctl start libvirtd"
        exit 1
    fi

    # Check if user is in libvirt group
    if ! groups | grep -q libvirt; then
        log_warn "User not in libvirt group. You may need sudo for virsh commands"
        log_warn "Add user to group: sudo usermod -a -G libvirt \$USER"
    fi

    # Check SSH key
    if [[ ! -f "$SSH_KEY" ]]; then
        log_error "SSH public key not found at: $SSH_KEY"
        log_error "Generate with: ssh-keygen -t ed25519 -C '<EMAIL>'"
        exit 1
    fi

    # Check if VM already exists
    if virsh list --all | grep -q "$VM_NAME"; then
        log_error "VM '$VM_NAME' already exists"
        log_error "Remove with: virsh destroy $VM_NAME && virsh undefine $VM_NAME"
        exit 1
    fi

    # Check network bridge
    if ! ip link show "$BRIDGE_IF" &>/dev/null; then
        log_warn "Bridge '$BRIDGE_IF' not found, will use default libvirt network"
        BRIDGE_IF="default"
    fi

    log_success "Prerequisites check passed"
}

download_nixos_image() {
    log_info "Preparing NixOS installation image..."

    if [[ -f "$BASE_IMG" ]]; then
        log_info "Using existing NixOS image: $BASE_IMG"
        return 0
    fi

    log_info "Downloading NixOS $NIXOS_RELEASE installation ISO..."
    log_info "URL: $NIXOS_IMG_URL"

    mkdir -p "$WORKDIR"

    if ! wget -O "$BASE_IMG" "$NIXOS_IMG_URL"; then
        log_error "Failed to download NixOS installation image"
        rm -f "$BASE_IMG"
        exit 1
    fi

    log_success "NixOS installation image downloaded: $BASE_IMG"
}

create_vm_disk() {
    log_info "Creating VM disk..."

    # Create a blank disk for NixOS installation
    if ! qemu-img create -f qcow2 "$DISK_IMG" "$DISK_SIZE"; then
        log_error "Failed to create VM disk"
        exit 1
    fi

    log_success "VM disk created: $DISK_IMG ($DISK_SIZE)"
}

generate_nixos_config() {
    log_info "Generating NixOS configuration..."

    mkdir -p "$CLOUD_INIT_DIR"

    local pub_key
    pub_key=$(cat "$SSH_KEY")

    # Generate NixOS configuration file
    cat >"${CLOUD_INIT_DIR}/configuration.nix" <<EOF
# NixOS Configuration for VM: $VM_NAME
{ config, pkgs, ... }:

{
  imports = [ ./hardware-configuration.nix ];

  # Boot loader
  boot.loader.grub.enable = true;
  boot.loader.grub.device = "/dev/vda";

  # Networking
  networking.hostName = "$VM_NAME";
  networking.networkmanager.enable = true;

  # Enable SSH
  services.openssh.enable = true;
  services.openssh.settings.PasswordAuthentication = false;
  services.openssh.settings.PermitRootLogin = "no";

  # User configuration
  users.users.$USERNAME = {
    isNormalUser = true;
    extraGroups = [ "wheel" "networkmanager" ];
    openssh.authorizedKeys.keys = [
      "$pub_key"
    ];
  };

  # Enable sudo for wheel group
  security.sudo.wheelNeedsPassword = false;

  # System packages
  environment.systemPackages = with pkgs; [
    vim
    wget
    curl
    git
    htop
    tree
    unzip
    qemu-guest-agent
  ];

  # Enable QEMU guest agent
  services.qemu-guest-agent.enable = true;

  # System version
  system.stateVersion = "$NIXOS_RELEASE";
}
EOF

    # Generate installation script
    cat >"${CLOUD_INIT_DIR}/install.sh" <<EOF
#!/usr/bin/env bash
set -euo pipefail

echo "Starting NixOS installation..."

# Partition the disk
parted /dev/vda -- mklabel msdos
parted /dev/vda -- mkpart primary 1MB -8GB
parted /dev/vda -- mkpart primary linux-swap -8GB 100%

# Format partitions
mkfs.ext4 -L nixos /dev/vda1
mkswap -L swap /dev/vda2

# Mount filesystems
mount /dev/disk/by-label/nixos /mnt
swapon /dev/vda2

# Generate hardware configuration
nixos-generate-config --root /mnt

# Copy our configuration
cp /tmp/configuration.nix /mnt/etc/nixos/

# Install NixOS
nixos-install --no-root-passwd

echo "NixOS installation completed!"
EOF

    chmod +x "${CLOUD_INIT_DIR}/install.sh"

    log_success "NixOS configuration generated"
}

wait_for_vm_boot() {
    log_info "Waiting for VM to boot from ISO..."

    local max_attempts=60
    local attempt=0

    while [[ $attempt -lt $max_attempts ]]; do
        if virsh list --state-running | grep -q "$VM_NAME"; then
            log_info "VM is running, waiting for console access..."
            sleep 10
            return 0
        fi
        sleep 5
        ((attempt++))
    done

    log_error "VM failed to boot within expected time"
    return 1
}

run_automated_install() {
    log_info "Starting automated NixOS installation..."

    # Wait for VM to boot
    if ! wait_for_vm_boot; then
        log_error "Failed to wait for VM boot"
        return 1
    fi

    log_info "Using simple automation approach..."

    # Create a single comprehensive installation script
    cat >"${CLOUD_INIT_DIR}/auto-nixos-install.sh" <<'INSTALL_SCRIPT'
#!/bin/bash
set -euo pipefail

echo "=== NixOS Automated Installation Started ==="
date

# Become root
sudo -i bash <<'ROOT_COMMANDS'
set -euo pipefail

echo "Partitioning disk..."
parted /dev/vda --script -- mklabel msdos
parted /dev/vda --script -- mkpart primary 1MB -8GB
parted /dev/vda --script -- mkpart primary linux-swap -8GB 100%

echo "Formatting partitions..."
mkfs.ext4 -L nixos /dev/vda1
mkswap -L swap /dev/vda2

echo "Mounting filesystems..."
mount /dev/disk/by-label/nixos /mnt
swapon /dev/vda2

echo "Generating hardware configuration..."
nixos-generate-config --root /mnt

echo "Installing NixOS configuration..."
INSTALL_SCRIPT

    # Add the configuration content
    echo 'cat > /mnt/etc/nixos/configuration.nix << '"'"'NIXOS_CONFIG_EOF'"'"'' >>"${CLOUD_INIT_DIR}/auto-nixos-install.sh"
    cat "${CLOUD_INIT_DIR}/configuration.nix" >>"${CLOUD_INIT_DIR}/auto-nixos-install.sh"
    echo 'NIXOS_CONFIG_EOF' >>"${CLOUD_INIT_DIR}/auto-nixos-install.sh"

    # Continue the script
    cat >>"${CLOUD_INIT_DIR}/auto-nixos-install.sh" <<'INSTALL_SCRIPT'

echo "Starting NixOS installation..."
nixos-install --no-root-passwd

echo "=== Installation Complete ==="
date
echo "Rebooting..."
reboot

ROOT_COMMANDS
INSTALL_SCRIPT

    chmod +x "${CLOUD_INIT_DIR}/auto-nixos-install.sh"

    # Use a much simpler approach - direct script execution via console
    log_info "Executing installation via console..."

    # Create a simple script that just runs the installation
    cat >"${CLOUD_INIT_DIR}/run-auto-install.sh" <<EOF
#!/bin/bash
set -euo pipefail

# Function to send text to console
send_to_vm() {
    local text="\$1"
    echo "\$text" | timeout 30 virsh console "$VM_NAME" --force 2>/dev/null || true
    sleep 2
}

# Wait for VM to be ready
echo "Waiting for VM to be ready..."
sleep 30

# Send the installation script
echo "Sending installation commands..."
send_to_vm "wget -O install.sh https://gist.githubusercontent.com/user/raw/install.sh && chmod +x install.sh && ./install.sh"

# Alternative: Use heredoc to send the entire script
timeout 60 virsh console "$VM_NAME" --force <<'CONSOLE_COMMANDS' || true
bash << 'NIXOS_INSTALL'
set -euo pipefail
sudo -i bash << 'ROOT_INSTALL'
parted /dev/vda --script -- mklabel msdos
parted /dev/vda --script -- mkpart primary 1MB -8GB
parted /dev/vda --script -- mkpart primary linux-swap -8GB 100%
mkfs.ext4 -L nixos /dev/vda1
mkswap -L swap /dev/vda2
mount /dev/disk/by-label/nixos /mnt
swapon /dev/vda2
nixos-generate-config --root /mnt
ROOT_INSTALL
NIXOS_INSTALL
CONSOLE_COMMANDS

EOF

    chmod +x "${CLOUD_INIT_DIR}/run-auto-install.sh"

    # Use the simplest possible approach - direct command injection
    log_info "Using direct command injection method..."

    # Wait for system to be ready
    sleep 30

    # Create a single command string that does everything
    local install_cmd="sudo bash -c '"
    install_cmd+="parted /dev/vda --script -- mklabel msdos && "
    install_cmd+="parted /dev/vda --script -- mkpart primary 1MB -8GB && "
    install_cmd+="parted /dev/vda --script -- mkpart primary linux-swap -8GB 100% && "
    install_cmd+="mkfs.ext4 -L nixos /dev/vda1 && "
    install_cmd+="mkswap -L swap /dev/vda2 && "
    install_cmd+="mount /dev/disk/by-label/nixos /mnt && "
    install_cmd+="swapon /dev/vda2 && "
    install_cmd+="nixos-generate-config --root /mnt && "
    install_cmd+="nixos-install --no-root-passwd && "
    install_cmd+="reboot'"

    log_info "Injecting installation command..."

    # Try to inject the command using a simple approach
    if timeout 120 bash -c "
        echo 'nixos' | virsh console '$VM_NAME' --force &
        sleep 5
        echo '' | virsh console '$VM_NAME' --force &
        sleep 5
        echo '$install_cmd' | virsh console '$VM_NAME' --force &
        sleep 10
        pkill -f 'virsh console $VM_NAME' || true
    " 2>/dev/null; then
        log_info "Installation command injected successfully"
    else
        log_warn "Command injection may have failed"
    fi

    # Monitor for completion
    log_info "Monitoring installation progress..."
    local max_wait=1800 # 30 minutes
    local waited=0

    while [[ $waited -lt $max_wait ]]; do
        # Check if VM has rebooted and has IP
        if virsh domifaddr "$VM_NAME" | grep -q "ipv4"; then
            local vm_ip
            vm_ip=$(virsh domifaddr "$VM_NAME" | grep ipv4 | awk '{print $4}' | cut -d'/' -f1)

            if timeout 5 ssh -o ConnectTimeout=3 -o StrictHostKeyChecking=no "$USERNAME@$vm_ip" echo "SSH test" 2>/dev/null; then
                log_success "Installation complete! VM is ready."
                log_success "SSH access: ssh $USERNAME@$vm_ip"
                return 0
            fi
        fi

        # Check disk writes to see if installation is active
        local disk_writes
        disk_writes=$(virsh domstats "$VM_NAME" --block 2>/dev/null | grep "block.0.wr.bytes=" | cut -d'=' -f2 || echo "0")

        if [[ "$disk_writes" -gt 0 ]]; then
            log_info "Installation active - ${disk_writes} bytes written to disk"
        else
            log_info "Waiting for installation to start writing to disk..."
        fi

        sleep 30
        ((waited += 30))
        log_info "Waited ${waited}s / ${max_wait}s..."
    done

    log_warn "Installation timeout - check manually with: virsh console $VM_NAME"
    return 1
}

launch_vm() {
    log_info "Launching VM '$VM_NAME'..."

    local network_config
    if [[ "$BRIDGE_IF" == "default" ]]; then
        network_config="network=default,model=virtio"
    else
        network_config="bridge=$BRIDGE_IF,model=virtio"
    fi

    # Use a known working Linux variant (nixos is not in osinfo-db)
    # We'll use debian11 as a safe fallback since NixOS is Linux-based
    local os_variant="debian11"

    # Check if debian11 is available, fallback to archlinux
    if ! osinfo-query os | grep -q "debian11"; then
        if osinfo-query os | grep -q "archlinux"; then
            os_variant="archlinux"
        else
            # Last resort - use ubuntu20.04 which should be widely available
            os_variant="ubuntu20.04"
        fi
    fi

    log_info "VM Configuration:"
    log_info "  Name: $VM_NAME"
    log_info "  Memory: ${RAM_MB}MB"
    log_info "  vCPUs: $VCPUS"
    log_info "  Disk: $DISK_IMG ($DISK_SIZE)"
    log_info "  Network: $network_config"
    log_info "  OS Variant: $os_variant"
    log_info "  Installation ISO: $BASE_IMG"

    if ! virt-install \
        --name "$VM_NAME" \
        --memory "$RAM_MB" \
        --vcpus "$VCPUS" \
        --disk path="$DISK_IMG",format=qcow2,bus=virtio \
        --cdrom "$BASE_IMG" \
        --os-variant "$os_variant" \
        --virt-type kvm \
        --graphics none \
        --network "$network_config" \
        --serial pty \
        --console pty,target_type=serial \
        --noautoconsole; then
        log_error "Failed to create VM"
        exit 1
    fi

    log_success "VM '$VM_NAME' created successfully!"

    # Add VM to /etc/hosts for easy access (after installation)
    log_info "VM will be available for SSH after NixOS installation is complete"
}

show_completion_info() {
    log_success "=== VM Creation Complete ==="
    echo
    log_info "VM Details:"
    echo "  Name: $VM_NAME"
    echo "  Memory: ${RAM_MB}MB"
    echo "  vCPUs: $VCPUS"
    echo "  Disk: $DISK_SIZE"
    echo "  Username: $USERNAME (after installation)"
    echo "  SSH Key: $SSH_KEY"
    echo "  NixOS Release: $NIXOS_RELEASE"
    echo
    log_info "Installation Process:"
    echo "  1. VM is booting from NixOS installation ISO"
    echo "  2. Connect to console: virsh console $VM_NAME"
    echo "  3. Manual installation required, or use automated script"
    echo
    log_info "Automated Installation (Optional):"
    echo "  The configuration files are ready in: $CLOUD_INIT_DIR"
    echo "  - configuration.nix: NixOS system configuration"
    echo "  - install.sh: Automated installation script"
    echo
    log_info "Manual Installation Steps:"
    echo "  1. Connect to console: virsh console $VM_NAME"
    echo "  2. Partition disk: sudo fdisk /dev/vda"
    echo "  3. Format and mount: sudo mkfs.ext4 /dev/vda1 && sudo mount /dev/vda1 /mnt"
    echo "  4. Generate config: sudo nixos-generate-config --root /mnt"
    echo "  5. Edit config: sudo nano /mnt/etc/nixos/configuration.nix"
    echo "  6. Install: sudo nixos-install"
    echo "  7. Reboot: sudo reboot"
    echo
    log_info "Useful Commands:"
    echo "  Check VM status:    virsh list --all"
    echo "  Start VM:           virsh start $VM_NAME"
    echo "  Stop VM:            virsh shutdown $VM_NAME"
    echo "  Force stop VM:      virsh destroy $VM_NAME"
    echo "  Delete VM:          virsh undefine $VM_NAME"
    echo "  Console access:     virsh console $VM_NAME"
    echo "  Get VM IP:          virsh domifaddr $VM_NAME"
    echo
    log_info "After Installation:"
    echo "  SSH access:         ssh $USERNAME@<VM_IP>"
    echo "  NixOS commands:"
    echo "    Update system:    sudo nixos-rebuild switch"
    echo "    Install packages: nix-env -iA nixpkgs.<package>"
    echo "    Search packages:  nix search nixpkgs <term>"
    echo "    Edit config:      sudo nano /etc/nixos/configuration.nix"
    echo
    log_warn "Note: NixOS requires manual installation. The VM is currently booting from ISO."
    log_warn "Use 'virsh console $VM_NAME' to access the installation environment."
}

# === Main Execution ===
main() {
    log_info "Starting NixOS VM creation..."
    log_info "VM Name: $VM_NAME"
    log_info "Working Directory: $WORKDIR"
    log_info "NixOS Release: $NIXOS_RELEASE"

    if [[ "$AUTO_INSTALL" == "true" ]]; then
        log_info "Auto-install mode enabled"
    fi

    check_prerequisites
    download_nixos_image
    create_vm_disk
    generate_nixos_config
    launch_vm

    if [[ "$AUTO_INSTALL" == "true" ]]; then
        log_info "Starting automated installation..."
        if run_automated_install; then
            log_success "NixOS VM created and installed successfully!"
            log_success "You can now SSH into the VM using the IP address shown above"
        else
            log_warn "Automated installation failed, falling back to manual installation"
            show_completion_info
        fi
    else
        show_completion_info
        log_success "NixOS VM is ready for installation!"
    fi
}

# Run main function
main "$@"

# # 1. Create the VM (already done)
# ./bin/create-nixos-vm --name test-nixos

# # 2. Connect to console for installation
# virsh console test-nixos

# # 3. In the NixOS installer, run the automated script:
# # (Copy the generated files and run the install script)

# # 4. After installation, reboot and SSH in
# ssh nixos@test-nixos
