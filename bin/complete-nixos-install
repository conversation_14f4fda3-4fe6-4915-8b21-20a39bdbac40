#!/bin/bash

# Quick NixOS Installation Completion Script
# This script helps complete the NixOS installation manually

set -euo pipefail

VM_NAME="${1:-nixos}"
WORKDIR="$HOME/${VM_NAME}-vm"
CLOUD_INIT_DIR="${WORKDIR}/cloud-init"

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() { echo -e "${BLUE}[INFO]${NC} $*"; }
log_success() { echo -e "${GREEN}[SUCCESS]${NC} $*"; }
log_warn() { echo -e "${YELLOW}[WARN]${NC} $*"; }
log_error() { echo -e "${RED}[ERROR]${NC} $*" >&2; }

show_installation_commands() {
    log_info "NixOS Installation Commands for VM: $VM_NAME"
    echo
    log_info "1. Connect to the VM console:"
    echo "   virsh console $VM_NAME"
    echo
    log_info "2. In the VM console, run these commands:"
    echo
    cat <<'EOF'
# Become root
sudo su -

# Partition the disk
parted /dev/vda --script -- mklabel msdos
parted /dev/vda --script -- mkpart primary 1MB -8GB
parted /dev/vda --script -- mkpart primary linux-swap -8GB 100%

# Format partitions
mkfs.ext4 -L nixos /dev/vda1
mkswap -L swap /dev/vda2

# Mount filesystems
mount /dev/disk/by-label/nixos /mnt
swapon /dev/vda2

# Generate hardware configuration
nixos-generate-config --root /mnt

# Copy the pre-generated configuration
EOF

    if [[ -f "${CLOUD_INIT_DIR}/configuration.nix" ]]; then
        echo "# Copy this configuration to /mnt/etc/nixos/configuration.nix:"
        echo "cat > /mnt/etc/nixos/configuration.nix << 'NIXOS_CONFIG_EOF'"
        cat "${CLOUD_INIT_DIR}/configuration.nix"
        echo "NIXOS_CONFIG_EOF"
    else
        echo "# Edit the configuration file:"
        echo "nano /mnt/etc/nixos/configuration.nix"
        echo "# Add your SSH key and enable SSH service"
    fi

    cat <<EOF

# Install NixOS
nixos-install --no-root-passwd

# Reboot
reboot
EOF

    echo
    log_info "3. After reboot, the VM should be accessible via SSH"
    echo
    log_info "4. To exit the console, press Ctrl+] (Ctrl+5 on some keyboards)"
    echo
}

check_vm_status() {
    log_info "Checking VM status..."
    
    if ! virsh list --all | grep -q "$VM_NAME"; then
        log_error "VM '$VM_NAME' not found"
        log_info "Available VMs:"
        virsh list --all
        exit 1
    fi
    
    local vm_state
    vm_state=$(virsh list --all | grep "$VM_NAME" | awk '{print $3}')
    
    log_info "VM '$VM_NAME' is $vm_state"
    
    if [[ "$vm_state" != "running" ]]; then
        log_warn "VM is not running. Starting VM..."
        virsh start "$VM_NAME"
        sleep 5
    fi
}

wait_for_installation() {
    log_info "Waiting for installation to complete..."
    log_info "This will monitor the VM and detect when it's ready for SSH"
    
    local max_wait=1800  # 30 minutes
    local waited=0
    
    while [[ $waited -lt $max_wait ]]; do
        # Check if VM has an IP address
        if virsh domifaddr "$VM_NAME" | grep -q "ipv4"; then
            local vm_ip
            vm_ip=$(virsh domifaddr "$VM_NAME" | grep ipv4 | awk '{print $4}' | cut -d'/' -f1)
            log_success "VM has IP address: $vm_ip"
            
            # Try to SSH (this will fail until installation is complete)
            if timeout 5 ssh -o ConnectTimeout=3 -o StrictHostKeyChecking=no nixos@"$vm_ip" echo "SSH test" 2>/dev/null; then
                log_success "SSH is working! Installation appears complete."
                log_success "You can now SSH to the VM: ssh nixos@$vm_ip"
                return 0
            fi
        fi
        
        sleep 30
        ((waited+=30))
        log_info "Waited ${waited}s / ${max_wait}s..."
    done
    
    log_warn "Timeout waiting for installation to complete"
    log_info "Check manually with: virsh console $VM_NAME"
}

main() {
    log_info "NixOS Installation Helper for VM: $VM_NAME"
    
    check_vm_status
    
    case "${2:-help}" in
        "commands")
            show_installation_commands
            ;;
        "wait")
            wait_for_installation
            ;;
        "status")
            virsh domifaddr "$VM_NAME"
            ;;
        *)
            echo "Usage: $0 [vm-name] [command]"
            echo
            echo "Commands:"
            echo "  commands  - Show installation commands to run in VM console"
            echo "  wait      - Wait for installation to complete and report IP"
            echo "  status    - Show VM IP address status"
            echo
            echo "Examples:"
            echo "  $0 nixos commands    # Show installation commands"
            echo "  $0 nixos wait        # Wait for installation to complete"
            echo "  $0 nixos status      # Check VM IP status"
            ;;
    esac
}

main "$@"
