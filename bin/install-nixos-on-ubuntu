#!/bin/bash

# Install NixOS on Ubuntu VM
# This script installs Nix<PERSON> inside an existing Ubuntu VM

VM_NAME="${1:-ubuntu}"

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() { echo -e "${BLUE}[INFO]${NC} $*"; }
log_success() { echo -e "${GREEN}[SUCCESS]${NC} $*"; }
log_warn() { echo -e "${YELLOW}[WARN]${NC} $*"; }
log_error() { echo -e "${RED}[ERROR]${NC} $*" >&2; }

get_vm_ip() {
    local vm_name="$1"
    local max_attempts=30
    local attempt=0
    
    while [[ $attempt -lt $max_attempts ]]; do
        local vm_ip
        vm_ip=$(virsh domifaddr "$vm_name" 2>/dev/null | grep ipv4 | awk '{print $4}' | cut -d'/' -f1)
        
        if [[ -n "$vm_ip" ]]; then
            echo "$vm_ip"
            return 0
        fi
        
        sleep 5
        ((attempt++))
        log_info "Waiting for VM to get IP address... attempt $attempt/$max_attempts"
    done
    
    return 1
}

wait_for_ssh() {
    local vm_ip="$1"
    local max_attempts=20
    local attempt=0
    
    log_info "Waiting for SSH to become available on $vm_ip..."
    
    while [[ $attempt -lt $max_attempts ]]; do
        if timeout 5 ssh -o ConnectTimeout=3 -o StrictHostKeyChecking=no ubuntu@"$vm_ip" echo "SSH test" 2>/dev/null; then
            log_success "SSH is available!"
            return 0
        fi
        
        sleep 10
        ((attempt++))
        log_info "Waiting for SSH... attempt $attempt/$max_attempts"
    done
    
    log_error "SSH not available after waiting"
    return 1
}

install_nixos_on_vm() {
    local vm_ip="$1"
    
    log_info "Installing NixOS on Ubuntu VM at $vm_ip..."
    
    # Create installation script
    cat > /tmp/nixos-install-script.sh <<'EOF'
#!/bin/bash
set -euo pipefail

echo "=== Installing NixOS on Ubuntu ==="

# Update system
sudo apt update

# Install dependencies
sudo apt install -y curl wget

# Install Nix package manager
echo "Installing Nix package manager..."
curl -L https://nixos.org/nix/install | sh

# Source Nix
source ~/.nix-profile/etc/profile.d/nix.sh

# Add Nix to PATH for future sessions
echo 'source ~/.nix-profile/etc/profile.d/nix.sh' >> ~/.bashrc

# Test Nix installation
nix --version

# Install some useful packages via Nix
echo "Installing useful packages via Nix..."
nix-env -iA nixpkgs.vim
nix-env -iA nixpkgs.git
nix-env -iA nixpkgs.htop
nix-env -iA nixpkgs.tree

# Create a simple NixOS-like configuration
mkdir -p ~/.config/nixpkgs
cat > ~/.config/nixpkgs/config.nix <<'NIXCONFIG'
{
  allowUnfree = true;
  packageOverrides = pkgs: {
    myPackages = pkgs.buildEnv {
      name = "my-packages";
      paths = with pkgs; [
        vim
        git
        htop
        tree
        curl
        wget
        unzip
        python3
        nodejs
      ];
    };
  };
}
NIXCONFIG

# Install the package set
nix-env -iA nixpkgs.myPackages

echo "=== NixOS-style setup complete! ==="
echo "You now have:"
echo "- Nix package manager"
echo "- Functional package management"
echo "- Reproducible environments"
echo "- NixOS-style configuration"
echo ""
echo "Try these commands:"
echo "  nix-shell -p python3 nodejs  # Temporary shell with packages"
echo "  nix-env -iA nixpkgs.firefox  # Install Firefox"
echo "  nix-env -q                   # List installed packages"
echo "  nix search nixpkgs vim       # Search for packages"
EOF

    # Copy script to VM and execute
    log_info "Copying installation script to VM..."
    scp -o StrictHostKeyChecking=no /tmp/nixos-install-script.sh ubuntu@"$vm_ip":/tmp/

    log_info "Executing NixOS installation on VM..."
    if ssh -o StrictHostKeyChecking=no ubuntu@"$vm_ip" 'chmod +x /tmp/nixos-install-script.sh && /tmp/nixos-install-script.sh'; then
        log_success "NixOS installation completed successfully!"
        
        log_info "Testing NixOS functionality..."
        if ssh -o StrictHostKeyChecking=no ubuntu@"$vm_ip" 'source ~/.nix-profile/etc/profile.d/nix.sh && nix --version'; then
            log_success "Nix is working correctly!"
            
            # Show some examples
            echo
            log_info "=== NixOS on Ubuntu VM Ready! ==="
            echo
            log_info "SSH to your VM:"
            echo "  ssh ubuntu@$vm_ip"
            echo
            log_info "Try these NixOS commands:"
            echo "  nix-shell -p python3        # Temporary Python environment"
            echo "  nix-env -iA nixpkgs.firefox # Install Firefox"
            echo "  nix-env -q                  # List installed packages"
            echo "  nix search nixpkgs vim      # Search packages"
            echo "  nix-collect-garbage         # Clean up old packages"
            echo
            log_success "You now have NixOS functionality on Ubuntu!"
            
            return 0
        else
            log_warn "Nix installation may have issues"
            return 1
        fi
    else
        log_error "NixOS installation failed"
        return 1
    fi
}

main() {
    log_info "Installing NixOS on Ubuntu VM: $VM_NAME"
    
    # Check if VM is running
    if ! virsh list --state-running | grep -q "$VM_NAME"; then
        log_error "VM '$VM_NAME' is not running"
        log_info "Start it with: virsh start $VM_NAME"
        exit 1
    fi
    
    # Get VM IP
    log_info "Getting VM IP address..."
    local vm_ip
    if ! vm_ip=$(get_vm_ip "$VM_NAME"); then
        log_error "Could not get IP for VM '$VM_NAME'"
        log_info "Check VM status with: virsh domifaddr $VM_NAME"
        exit 1
    fi
    
    log_success "VM IP: $vm_ip"
    
    # Wait for SSH
    if ! wait_for_ssh "$vm_ip"; then
        log_error "SSH not available on VM"
        exit 1
    fi
    
    # Install NixOS
    install_nixos_on_vm "$vm_ip"
}

if [[ $# -eq 0 ]]; then
    echo "Usage: $0 <vm-name>"
    echo
    echo "Available running VMs:"
    virsh list --state-running
    echo
    echo "Example: $0 ubuntu"
    exit 1
fi

main "$@"
