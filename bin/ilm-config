#! /usr/bin/env bash

# shellcheck disable=SC1090
source <(curl -sSL https://raw.githubusercontent.com/pervezfunctor/dotfiles/refs/heads/main/share/utils)

INSTALL_OPTIONS=("nvim" "bash" "vscode_flatpak" "vscode" "zsh" "tmux" "emacs" "git" "gnome" "sway" "hyprland")

DEFAULT_OPTIONS=("zsh" "tmux" "nvim")

help() {
    echo "Usage: $0 [OPTION1] [OPTION2] ..."
    echo "Options:"
    echo "  zsh         zsh config with starship prompt and common plugins."
    echo "  tmux        tmux config, catppuccin theme and common plugins."
    echo "  nvim        lazyvim based config and plugins."
    echo "  vscode      vscode settings."
    echo "  emacs       doom emacs config."
    echo "  git         git config."
    echo "  sway        sway config."
    echo "  hyprland    hyprland config."
    echo "  gnome       gnome config."
    echo "  help        show this help."
    echo ""
    echo ""
}

ilm-config() {
    while [[ "$#" -gt 0 ]]; do
        if fn_exists "${1}_config_install"; then
            "${1}_config_install"
        else
            err_exit "No such config: $1"
        fi
        shift
    done
}

main() {
    if ! [[ "$#" -eq 0 ]]; then
        ilm-config "$@"
        return 0
    fi

    if ! has_cmd gum; then
        help
        exit 0
    fi

    SELECTED_OPTIONS=$(igum choose "${INSTALL_OPTIONS[@]}" --no-limit --selected "${DEFAULT_OPTIONS[@]}" --header "Choose options to install" | tr '\n' ' ')

    if [[ -n "$SELECTED_OPTIONS" ]]; then
        echo ""
        slog "Selected: ${SELECTED_OPTIONS}. Configuring..."
        echo ""
        ilm-config "$SELECTED_OPTIONS"
        slog "Configuration complete."
    else
        slog "No options selected."
        return 1
    fi
}

CLICOLOR_FORCE=1 bootstrap "$@"
