#!/bin/bash

# Script manages /etc/hosts entries for VMs

set -euo pipefail

RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

log_info() { echo -e "${BLUE}[INFO]${NC} $*"; }
log_success() { echo -e "${GREEN}[SUCCESS]${NC} $*"; }
log_warn() { echo -e "${YELLOW}[WARN]${NC} $*"; }
log_error() { echo -e "${RED}[ERROR]${NC} $*" >&2; }

usage() {
    cat <<EOF
Usage: $0 <command> [options]

Manage /etc/hosts entries for VMs automatically.

COMMANDS:
    add <vm-name>           Add VM to /etc/hosts (auto-detect IP)
    remove <vm-name>        Remove VM from /etc/hosts
    list                    List all VM entries in /etc/hosts
    update                  Update all running VMs in /etc/hosts
    clean                   Remove entries for non-existent VMs

EXAMPLES:
    $0 add docker           # Add docker VM to /etc/hosts
    $0 remove old-vm        # Remove old-vm from /etc/hosts
    $0 update               # Update all running VMs
    $0 list                 # Show all VM entries

EOF
}

get_vm_ip() {
    local vm_name="$1"

    local ip
    ip=$(virsh domifaddr "$vm_name" 2>/dev/null | awk '/ipv4/ {print $4}' | cut -d'/' -f1 | head -1)

    if [[ -n "$ip" ]]; then
        echo "$ip"
        return 0
    fi

    ip=$(sudo virsh net-dhcp-leases default 2>/dev/null | grep "$vm_name" | awk '{print $5}' | cut -d'/' -f1 | head -1)

    if [[ -n "$ip" ]]; then
        echo "$ip"
        return 0
    fi

    return 1
}

add_vm_to_hosts() {
    local vm_name="$1"

    if ! virsh dominfo "$vm_name" &>/dev/null; then
        log_error "VM '$vm_name' not found"
        return 1
    fi

    local ip
    if ! ip=$(get_vm_ip "$vm_name"); then
        log_error "Could not get IP for VM '$vm_name'. Is it running?"
        return 1
    fi

    # Check if entry already exists
    if grep -q "^[0-9.]* .*$vm_name" /etc/hosts; then
        log_warn "Entry for '$vm_name' already exists in /etc/hosts"
        # Update existing entry
        sudo sed -i "/^[0-9.]* .*$vm_name/d" /etc/hosts
    fi

    echo "$ip $vm_name" | sudo tee -a /etc/hosts >/dev/null
    log_success "Added $vm_name ($ip) to /etc/hosts"
}

remove_vm_from_hosts() {
    local vm_name="$1"

    if grep -q "^[0-9.]* .*$vm_name" /etc/hosts; then
        sudo sed -i "/^[0-9.]* .*$vm_name/d" /etc/hosts
        log_success "Removed $vm_name from /etc/hosts"
    else
        log_warn "No entry found for '$vm_name' in /etc/hosts"
    fi
}

list_vm_hosts() {
    log_info "VM entries in /etc/hosts:"
    echo

    if grep -E "^192\.168\.122\.[0-9]+" /etc/hosts; then
        echo
    else
        log_warn "No VM entries found in /etc/hosts"
    fi
}

update_all_vms() {
    log_info "Updating /etc/hosts for all running VMs..."

    local vms
    vms=$(virsh list --name)

    if [[ -z "$vms" ]]; then
        log_warn "No running VMs found"
        return 0
    fi

    while IFS= read -r vm; do
        if [[ -n "$vm" ]]; then
            log_info "Processing VM: $vm"
            add_vm_to_hosts "$vm"
        fi
    done <<<"$vms"

    log_success "Updated /etc/hosts for all running VMs"
}

clean_vm_hosts() {
    log_info "Cleaning up /etc/hosts entries for non-existent VMs..."

    local host_vms
    host_vms=$(grep -E "^192\.168\.122\.[0-9]+" /etc/hosts | awk '{print $2}' || true)

    if [[ -z "$host_vms" ]]; then
        log_info "No VM entries found in /etc/hosts"
        return 0
    fi

    while IFS= read -r vm; do
        if [[ -n "$vm" ]]; then
            if ! virsh dominfo "$vm" &>/dev/null; then
                log_warn "VM '$vm' no longer exists, removing from /etc/hosts"
                remove_vm_from_hosts "$vm"
            fi
        fi
    done <<<"$host_vms"

    log_success "Cleanup complete"
}

if [[ $# -eq 0 ]]; then
    usage
    exit 1
fi

command="$1"
vm_name="${2:-}"

case "$command" in
add)
    [[ -z "$vm_name" ]] && {
        log_error "VM name required"
        usage
        exit 1
    }
    add_vm_to_hosts "$vm_name"
    ;;
remove)
    [[ -z "$vm_name" ]] && {
        log_error "VM name required"
        usage
        exit 1
    }
    remove_vm_from_hosts "$vm_name"
    ;;
list)
    list_vm_hosts
    ;;
update)
    update_all_vms
    ;;
clean)
    clean_vm_hosts
    ;;
--help | -h)
    usage
    ;;
*)
    log_error "Unknown command: $command"
    usage
    exit 1
    ;;
esac
