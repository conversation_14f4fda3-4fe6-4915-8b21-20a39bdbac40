#!/bin/bash

# Install Nix Package Manager on Existing VM
# This gives you NixOS-like functionality without installation issues

VM_NAME="${1:-test-fedora}"

# Colors
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
NC='\033[0m'

log_info() { echo -e "${BLUE}[INFO]${NC} $*"; }
log_success() { echo -e "${GREEN}[SUCCESS]${NC} $*"; }
log_warn() { echo -e "${YELLOW}[WARN]${NC} $*"; }

get_vm_ip() {
    local vm_name="$1"
    virsh domifaddr "$vm_name" | grep ipv4 | awk '{print $4}' | cut -d'/' -f1
}

install_nix() {
    local vm_ip="$1"
    
    log_info "Installing Nix package manager on VM at $vm_ip..."
    
    # Install Nix
    ssh -o StrictHostKeyChecking=no "$vm_ip" 'curl -L https://nixos.org/nix/install | sh'
    
    # Source Nix
    ssh -o StrictHostKeyChecking=no "$vm_ip" 'source ~/.nix-profile/etc/profile.d/nix.sh'
    
    # Test Nix installation
    if ssh -o StrictHostKeyChecking=no "$vm_ip" 'nix --version' 2>/dev/null; then
        log_success "Nix installed successfully!"
        
        # Show some example commands
        echo
        log_info "You can now use Nix on your VM:"
        echo "  ssh $vm_ip"
        echo "  nix-env -iA nixpkgs.vim     # Install vim"
        echo "  nix-env -iA nixpkgs.git     # Install git"
        echo "  nix-shell -p python3        # Temporary Python shell"
        echo "  nix search nixpkgs firefox  # Search packages"
        
        return 0
    else
        log_warn "Nix installation may have failed"
        return 1
    fi
}

main() {
    log_info "Installing Nix on VM: $VM_NAME"
    
    # Check if VM is running
    if ! virsh list --state-running | grep -q "$VM_NAME"; then
        log_warn "VM '$VM_NAME' is not running"
        log_info "Available running VMs:"
        virsh list --state-running
        exit 1
    fi
    
    # Get VM IP
    local vm_ip
    vm_ip=$(get_vm_ip "$VM_NAME")
    
    if [[ -z "$vm_ip" ]]; then
        log_warn "Could not get IP for VM '$VM_NAME'"
        exit 1
    fi
    
    log_info "VM IP: $vm_ip"
    
    # Test SSH connectivity
    if ! timeout 5 ssh -o ConnectTimeout=3 -o StrictHostKeyChecking=no "$vm_ip" echo "SSH test" 2>/dev/null; then
        log_warn "Cannot SSH to VM. Make sure SSH is working."
        exit 1
    fi
    
    # Install Nix
    install_nix "$vm_ip"
}

if [[ $# -eq 0 ]]; then
    echo "Usage: $0 <vm-name>"
    echo
    echo "Available running VMs:"
    virsh list --state-running
    echo
    echo "Example: $0 test-fedora"
    exit 1
fi

main "$@"
