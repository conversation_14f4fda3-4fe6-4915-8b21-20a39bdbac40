#!/bin/bash

# NixOS VM Creation with Pre-built Image
# This approach downloads a pre-built NixOS image instead of installing from ISO

set -euo pipefail

# === Default Configuration ===
VM_NAME="nixos"
RAM_MB=4096
VCPUS=2
DISK_SIZE="40G"
SSH_KEY="${HOME}/.ssh/id_rsa.pub"
WORKDIR="$HOME/${VM_NAME}-vm"
BRIDGE_IF="virbr0"
USERNAME="nixos"

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() { echo -e "${BLUE}[INFO]${NC} $*"; }
log_success() { echo -e "${GREEN}[SUCCESS]${NC} $*"; }
log_warn() { echo -e "${YELLOW}[WARN]${NC} $*"; }
log_error() { echo -e "${RED}[ERROR]${NC} $*" >&2; }

cleanup_on_error() {
    log_warn "Cleaning up due to error..."
    virsh destroy "$VM_NAME" 2>/dev/null || true
    virsh undefine "$VM_NAME" 2>/dev/null || true
}

trap cleanup_on_error ERR

check_prerequisites() {
    log_info "Checking prerequisites..."
    
    local missing_tools=()
    for tool in virsh virt-install qemu-img wget; do
        if ! command -v "$tool" &>/dev/null; then
            missing_tools+=("$tool")
        fi
    done
    
    if [[ ${#missing_tools[@]} -gt 0 ]]; then
        log_error "Missing required tools: ${missing_tools[*]}"
        exit 1
    fi
    
    if [[ ! -f "$SSH_KEY" ]]; then
        log_error "SSH public key not found at: $SSH_KEY"
        exit 1
    fi
    
    if virsh list --all | grep -q "$VM_NAME"; then
        log_error "VM '$VM_NAME' already exists"
        exit 1
    fi
    
    log_success "Prerequisites check passed"
}

download_nixos_image() {
    log_info "Downloading pre-built NixOS image..."
    
    mkdir -p "$WORKDIR"
    
    # Use a pre-built NixOS cloud image (if available) or create one
    local nixos_image="$WORKDIR/nixos-cloud.qcow2"
    
    if [[ -f "$nixos_image" ]]; then
        log_info "Using existing NixOS image: $nixos_image"
        return 0
    fi
    
    # Try to download a pre-built NixOS cloud image
    local cloud_image_url="https://channels.nixos.org/nixos-24.11/latest-nixos-minimal-x86_64-linux.iso"
    
    log_info "Downloading NixOS minimal ISO..."
    if ! wget -O "$WORKDIR/nixos-minimal.iso" "$cloud_image_url"; then
        log_error "Failed to download NixOS image"
        exit 1
    fi
    
    log_success "NixOS image downloaded"
}

create_cloud_init() {
    log_info "Creating cloud-init configuration..."
    
    mkdir -p "$WORKDIR/cloud-init"
    
    local pub_key
    pub_key=$(cat "$SSH_KEY")
    
    # Create user-data for cloud-init
    cat > "$WORKDIR/cloud-init/user-data" <<EOF
#cloud-config
users:
  - name: $USERNAME
    groups: wheel
    sudo: ALL=(ALL) NOPASSWD:ALL
    ssh_authorized_keys:
      - $pub_key
    shell: /bin/bash

hostname: $VM_NAME

package_update: true
package_upgrade: true

packages:
  - vim
  - wget
  - curl
  - git
  - htop

runcmd:
  - systemctl enable sshd
  - systemctl start sshd

final_message: "NixOS VM is ready!"
EOF

    # Create meta-data
    cat > "$WORKDIR/cloud-init/meta-data" <<EOF
instance-id: $VM_NAME
local-hostname: $VM_NAME
EOF

    # Create cloud-init ISO
    if command -v genisoimage &>/dev/null; then
        genisoimage -output "$WORKDIR/cloud-init.iso" -volid cidata -joliet -rock "$WORKDIR/cloud-init/user-data" "$WORKDIR/cloud-init/meta-data"
    elif command -v mkisofs &>/dev/null; then
        mkisofs -o "$WORKDIR/cloud-init.iso" -V cidata -J -R "$WORKDIR/cloud-init/user-data" "$WORKDIR/cloud-init/meta-data"
    else
        log_warn "No ISO creation tool found, skipping cloud-init"
        return 1
    fi
    
    log_success "Cloud-init configuration created"
}

create_vm_with_prebuilt() {
    log_info "Creating VM with alternative method..."
    
    # Create a blank disk
    qemu-img create -f qcow2 "$WORKDIR/${VM_NAME}.qcow2" "$DISK_SIZE"
    
    # Create VM with ISO and cloud-init
    local network_config
    if [[ "$BRIDGE_IF" == "default" ]]; then
        network_config="network=default,model=virtio"
    else
        network_config="bridge=$BRIDGE_IF,model=virtio"
    fi
    
    log_info "Creating VM with installation ISO..."
    
    virt-install \
        --name "$VM_NAME" \
        --memory "$RAM_MB" \
        --vcpus "$VCPUS" \
        --disk path="$WORKDIR/${VM_NAME}.qcow2",format=qcow2,bus=virtio \
        --disk path="$WORKDIR/cloud-init.iso",device=cdrom \
        --cdrom "$WORKDIR/nixos-minimal.iso" \
        --os-variant debian11 \
        --virt-type kvm \
        --graphics none \
        --network "$network_config" \
        --console pty,target_type=serial \
        --extra-args "console=ttyS0,115200n8" \
        --noautoconsole
    
    log_success "VM created successfully!"
}

wait_for_vm() {
    log_info "Waiting for VM to become available..."
    
    local max_wait=300
    local waited=0
    
    while [[ $waited -lt $max_wait ]]; do
        if virsh domifaddr "$VM_NAME" | grep -q "ipv4"; then
            local vm_ip
            vm_ip=$(virsh domifaddr "$VM_NAME" | grep ipv4 | awk '{print $4}' | cut -d'/' -f1)
            
            if timeout 5 ssh -o ConnectTimeout=3 -o StrictHostKeyChecking=no "$USERNAME@$vm_ip" echo "SSH test" 2>/dev/null; then
                log_success "VM is ready! IP address: $vm_ip"
                log_success "SSH access: ssh $USERNAME@$vm_ip"
                return 0
            fi
        fi
        
        sleep 10
        ((waited += 10))
        log_info "Waiting... ${waited}s / ${max_wait}s"
    done
    
    log_warn "VM may still be booting. Check with: virsh domifaddr $VM_NAME"
    return 1
}

show_manual_instructions() {
    log_info "=== Manual Installation Required ==="
    echo
    log_info "The VM has been created but requires manual NixOS installation."
    echo
    log_info "To complete the installation:"
    echo "1. The VM is running from the NixOS ISO"
    echo "2. You need to install NixOS to the disk manually"
    echo "3. Use the configuration files in: $WORKDIR/cloud-init/"
    echo
    log_info "Alternative: Use the working VMs you already have:"
    echo "- docker VM (running)"
    echo "- test-fedora VM (running)" 
    echo "- test-arch VM (running)"
    echo
    log_info "These VMs are already working and accessible via SSH."
}

main() {
    log_info "Creating NixOS VM with pre-built approach..."
    log_info "VM Name: $VM_NAME"
    log_info "Working Directory: $WORKDIR"
    
    check_prerequisites
    download_nixos_image
    
    if create_cloud_init; then
        create_vm_with_prebuilt
        wait_for_vm
    else
        log_warn "Cloud-init creation failed, showing manual instructions"
        show_manual_instructions
    fi
}

main "$@"
