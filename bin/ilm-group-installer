#! /usr/bin/env bash

# shellcheck disable=SC1090
source <(curl -sSL https://raw.githubusercontent.com/pervezfunctor/dotfiles/refs/heads/main/share/utils)

INSTALL_OPTIONS=("nix" "min" "shell" "ct" "vm" "work" "desktop" "prog" "all" "sway" "hyprland")

help() {
    echo "Usage: $0 [OPTION]"
    echo "Options:"
    echo "  nix         nix installation"
    echo "  min         essential packages only."
    echo "  shell       min and shell tools and config for zsh, tmux, neovim."
    echo "  ct          shell and container tools - docker, podman, incus, distrobox."
    echo "  vm          ct and vm tools like buildah, libvirt."
    echo "  work        min and vscode, flatpak apps."
    echo "  desktop     vm and desktop apps like vscode, zoom, obsidian."
    echo "  prog        vm and programming tools for rust, go, web, c++, python."
    echo "  all         everything(not recommended)."
    echo "  sway        sway desktop on fedora and tumbleweed."
    echo "  hyprland    hyprland desktop on fedora and tumbleweed."
    echo "  help        show this help."
    echo ""
}

main() {
    if ! [[ "$#" -eq 0 ]]; then
        "${1}_groupstall"
        return 0
    fi

    if ! has_cmd gum; then
        help
        exit 0
    fi

    SELECTED_OPTION=$(igum choose "${INSTALL_OPTIONS[@]}" --header "Choose option to install" | tr '\n' ' ')

    if [[ -n "$SELECTED_OPTION" ]]; then
        echo ""
        slog "Selected: ${SELECTED_OPTION}. Installing.."
        echo ""
        "${1}_groupstall"
        slog "Installation complete."
    else
        slog "No option selected."
        return 1
    fi
}

if [[ "$1" == "help" ]]; then
    help
else
    CLICOLOR_FORCE=1 bootstrap "$@"
fi
