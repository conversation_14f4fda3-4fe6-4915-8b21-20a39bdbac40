#!/bin/bash

# Quick NixOS Installation Helper
# This script provides the exact commands to run in the VM console

VM_NAME="${1:-nixos}"
WORKDIR="$HOME/${VM_NAME}-vm"
CLOUD_INIT_DIR="${WORKDIR}/cloud-init"

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() { echo -e "${BLUE}[INFO]${NC} $*"; }
log_success() { echo -e "${GREEN}[SUCCESS]${NC} $*"; }
log_warn() { echo -e "${YELLOW}[WARN]${NC} $*"; }

show_install_commands() {
    log_info "Quick NixOS Installation for VM: $VM_NAME"
    echo
    log_info "1. Connect to VM console:"
    echo "   virsh console $VM_NAME"
    echo
    log_info "2. Copy and paste these commands (one block at a time):"
    echo
    
    echo "# === STEP 1: Become root ==="
    echo "sudo -i"
    echo
    
    echo "# === STEP 2: Partition and format disk ==="
    cat <<'EOF'
parted /dev/vda --script -- mklabel msdos
parted /dev/vda --script -- mkpart primary 1MB -8GB
parted /dev/vda --script -- mkpart primary linux-swap -8GB 100%
mkfs.ext4 -L nixos /dev/vda1
mkswap -L swap /dev/vda2
mount /dev/disk/by-label/nixos /mnt
swapon /dev/vda2
EOF
    echo
    
    echo "# === STEP 3: Generate hardware config ==="
    echo "nixos-generate-config --root /mnt"
    echo
    
    echo "# === STEP 4: Install configuration ==="
    echo "cat > /mnt/etc/nixos/configuration.nix << 'NIXOS_CONFIG_EOF'"
    
    if [[ -f "${CLOUD_INIT_DIR}/configuration.nix" ]]; then
        cat "${CLOUD_INIT_DIR}/configuration.nix"
    else
        echo "# Configuration file not found!"
        echo "# Run create-nixos-vm first to generate configuration"
    fi
    
    echo "NIXOS_CONFIG_EOF"
    echo
    
    echo "# === STEP 5: Install NixOS ==="
    echo "nixos-install --no-root-passwd"
    echo
    
    echo "# === STEP 6: Reboot ==="
    echo "reboot"
    echo
    
    log_info "3. After reboot, check IP and SSH:"
    echo "   virsh domifaddr $VM_NAME"
    echo "   ssh nixos@<VM_IP>"
}

create_install_script() {
    log_info "Creating installation script file..."
    
    cat > "${CLOUD_INIT_DIR}/quick-install.sh" <<'EOF'
#!/bin/bash
set -euo pipefail

echo "=== NixOS Quick Installation ==="

# Become root
sudo -i

# Partition disk
parted /dev/vda --script -- mklabel msdos
parted /dev/vda --script -- mkpart primary 1MB -8GB
parted /dev/vda --script -- mkpart primary linux-swap -8GB 100%

# Format partitions
mkfs.ext4 -L nixos /dev/vda1
mkswap -L swap /dev/vda2

# Mount filesystems
mount /dev/disk/by-label/nixos /mnt
swapon /dev/vda2

# Generate hardware config
nixos-generate-config --root /mnt

# Copy configuration (you need to do this manually)
echo "Now copy the configuration to /mnt/etc/nixos/configuration.nix"
echo "Then run: nixos-install --no-root-passwd"
echo "Then run: reboot"
EOF

    chmod +x "${CLOUD_INIT_DIR}/quick-install.sh"
    log_success "Installation script created: ${CLOUD_INIT_DIR}/quick-install.sh"
}

run_simple_automation() {
    log_info "Attempting simple automation..."
    
    # Check if VM is running
    if ! virsh list --state-running | grep -q "$VM_NAME"; then
        log_warn "VM '$VM_NAME' is not running"
        return 1
    fi
    
    log_info "Trying to execute installation via console..."
    
    # Create a simple script that sends commands
    cat > "${CLOUD_INIT_DIR}/send-commands.sh" <<EOF
#!/bin/bash

# Send commands to VM console
send_cmd() {
    local cmd="\$1"
    echo "Sending: \$cmd"
    echo "\$cmd" | virsh console "$VM_NAME" --force &
    sleep 3
    pkill -f "virsh console $VM_NAME" || true
    sleep 2
}

# Login and become root
send_cmd "nixos"
send_cmd ""
send_cmd "sudo -i"

# Partition commands
send_cmd "parted /dev/vda --script -- mklabel msdos"
send_cmd "parted /dev/vda --script -- mkpart primary 1MB -8GB"
send_cmd "parted /dev/vda --script -- mkpart primary linux-swap -8GB 100%"

# Format commands
send_cmd "mkfs.ext4 -L nixos /dev/vda1"
send_cmd "mkswap -L swap /dev/vda2"

# Mount commands
send_cmd "mount /dev/disk/by-label/nixos /mnt"
send_cmd "swapon /dev/vda2"

# Generate config
send_cmd "nixos-generate-config --root /mnt"

echo "Basic setup complete. You need to manually:"
echo "1. Copy configuration to /mnt/etc/nixos/configuration.nix"
echo "2. Run: nixos-install --no-root-passwd"
echo "3. Run: reboot"
EOF

    chmod +x "${CLOUD_INIT_DIR}/send-commands.sh"
    
    if "${CLOUD_INIT_DIR}/send-commands.sh"; then
        log_success "Basic commands sent to VM"
        log_info "You still need to manually complete the installation"
    else
        log_warn "Automation failed, use manual method"
    fi
}

case "${2:-commands}" in
    "commands")
        show_install_commands
        ;;
    "script")
        create_install_script
        ;;
    "auto")
        run_simple_automation
        ;;
    *)
        echo "Usage: $0 [vm-name] [commands|script|auto]"
        echo "  commands - Show installation commands to copy/paste"
        echo "  script   - Create installation script file"
        echo "  auto     - Attempt simple automation"
        ;;
esac
