#! /usr/bin/env bash

if command -v curl >/dev/null; then
    # shellcheck disable=SC1090
    source <(curl -sSL https://raw.githubusercontent.com/pervezfunctor/dotfiles/refs/heads/main/share/utils)
elif command -v wget >/dev/null; then
    # shellcheck disable=SC1090
    source <(wget -qO- https://raw.githubusercontent.com/pervezfunctor/dotfiles/refs/heads/main/share/utils)
else
    echo "curl or wget not found. Please install curl or wget and try again."
    exit 1
fi

# Check if the script is run as root
[[ $EUID -ne 0 ]] && err_exit "This script must be run as root" 1>&2

# Check if username is provided
[[ "$#" -ne 1 ]] && err_exit "Usage: $0 username" 1>&2

USERNAME=$1

if id "$USERNAME" &>/dev/null; then
    echo "User '$USERNAME' already exists." >&2
    echo "Setting password for existing user..."
    passwd "$USERNAME" || err_exit "Failed to set password"
elif useradd -m "$USERNAME"; then
    slog "User created successfully"
else
    err_exit "Failed to create user"
fi

# Determine the sudo group based on the system
if is_apt; then
    apt-get update && apt-get install -y sudo
    SUDO_GROUP="sudo"
elif is_rh; then
    dnf install -y sudo
    SUDO_GROUP="wheel"
elif is_tw; then
    zypper install -y sudo
    SUDO_GROUP="wheel"
else
    err_exit "Unsupported system"
fi

if ! getent group "$SUDO_GROUP" >/dev/null; then
    echo "Creating group '$SUDO_GROUP'..."
    groupadd "$SUDO_GROUP"
fi

# Add the user to the sudo group
usermod -aG "$SUDO_GROUP" "$USERNAME"

slog "$USERNAME now has sudo privileges"
