#! /usr/bin/env bash

DOT_DIR=${DOT_DIR:-$HOME/.ilm}
source $DOT_DIR/share/utils

if cat ~/.dotfiles-output.log | grep -i denied; then
  press_enter
fi

if cat ~/.dotfiles-output.log | grep -i fail; then
  press_enter
fi

if cat ~/.dotfiles-output.log | grep -i warn; then
  press_enter
fi

if cat ~/.dotfiles-output.log | grep -i error; then
  press_enter
fi

if cat ~/.dotfiles-output.log | grep -i not; then
  press_enter
fi

if cat ~/.dotfiles-output.log | grep -i unable; then
  press_enter
fi

if cat ~/.dotfiles-output.log | grep -i denied; then
  press_enter
fi

if cat ~/.dotfiles-output.log | grep -i fatal; then
  press_enter
fi

if cat ~/.dotfiles-error.log | grep -i fail; then
  press_enter
fi

if cat ~/.dotfiles-error.log | grep -i warn; then
  press_enter
fi

if cat ~/.dotfiles-error.log | grep -i error; then
  press_enter
fi

if cat ~/.dotfiles-error.log | grep -i not; then
  press_enter
fi

if cat ~/.dotfiles-error.log | grep -i unable; then
  press_enter
fi

if cat ~/.dotfiles-error.log | grep -i fatal; then
  press_enter
fi
