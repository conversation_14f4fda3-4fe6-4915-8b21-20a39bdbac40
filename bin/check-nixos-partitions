#!/bin/bash

# Check NixOS VM Partitions

VM_NAME="${1:-nixos}"

# Colors
GREEN='\033[0;32m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() { echo -e "${BLUE}[INFO]${NC} $*"; }
log_success() { echo -e "${GREEN}[SUCCESS]${NC} $*"; }

check_partitions() {
    log_info "Checking partitions for VM: $VM_NAME"
    
    # Check if VM has IP address
    if virsh domifaddr "$VM_NAME" | grep -q "ipv4"; then
        local vm_ip
        vm_ip=$(virsh domifaddr "$VM_NAME" | grep ipv4 | awk '{print $4}' | cut -d'/' -f1)
        log_success "VM IP: $vm_ip"
        
        # Try to SSH and check partitions
        if timeout 10 ssh -o ConnectTimeout=5 -o StrictHostKeyChecking=no nixos@"$vm_ip" "lsblk" 2>/dev/null; then
            log_success "Partition check complete via SSH"
        else
            log_info "SSH not ready yet, cannot check partitions remotely"
        fi
    else
        log_info "VM has no IP address yet - installation likely still in progress"
        log_info "Expected partition layout:"
        echo "  /dev/vda1: ~32GB ext4 (root filesystem)"
        echo "  /dev/vda2: 8GB swap"
    fi
}

check_partitions
