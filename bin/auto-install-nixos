#!/bin/bash

# Automated NixOS Installation Script
# This script runs the installation commands directly via SSH or console injection

set -euo pipefail

VM_NAME="${1:-nixos}"
WORKDIR="$HOME/${VM_NAME}-vm"
CLOUD_INIT_DIR="${WORKDIR}/cloud-init"

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() { echo -e "${BLUE}[INFO]${NC} $*"; }
log_success() { echo -e "${GREEN}[SUCCESS]${NC} $*"; }
log_warn() { echo -e "${YELLOW}[WARN]${NC} $*"; }
log_error() { echo -e "${RED}[ERROR]${NC} $*" >&2; }

wait_for_vm_ready() {
    log_info "Waiting for VM to be ready for installation..."
    
    local max_attempts=60
    local attempt=0
    
    while [[ $attempt -lt $max_attempts ]]; do
        if virsh list --state-running | grep -q "$VM_NAME"; then
            log_info "VM is running, waiting for system to be ready..."
            sleep 10
            return 0
        fi
        sleep 5
        ((attempt++))
    done
    
    log_error "VM failed to become ready within expected time"
    return 1
}

create_installation_script() {
    log_info "Creating installation script..."
    
    # Create the installation script that will be injected into the VM
    cat > "${CLOUD_INIT_DIR}/run-install.sh" <<'EOF'
#!/bin/bash
set -euo pipefail

echo "=== Starting Automated NixOS Installation ==="

# Become root
if [[ $EUID -ne 0 ]]; then
    exec sudo "$0" "$@"
fi

# Partition the disk
echo "Partitioning disk..."
parted /dev/vda --script -- mklabel msdos
parted /dev/vda --script -- mkpart primary 1MB -8GB
parted /dev/vda --script -- mkpart primary linux-swap -8GB 100%

# Format partitions
echo "Formatting partitions..."
mkfs.ext4 -L nixos /dev/vda1
mkswap -L swap /dev/vda2

# Mount filesystems
echo "Mounting filesystems..."
mount /dev/disk/by-label/nixos /mnt
swapon /dev/vda2

# Generate hardware configuration
echo "Generating hardware configuration..."
nixos-generate-config --root /mnt

# Copy our configuration
echo "Installing NixOS configuration..."
EOF

    # Append the configuration content
    echo 'cat > /mnt/etc/nixos/configuration.nix << '"'"'NIXOS_CONFIG_EOF'"'"'' >> "${CLOUD_INIT_DIR}/run-install.sh"
    cat "${CLOUD_INIT_DIR}/configuration.nix" >> "${CLOUD_INIT_DIR}/run-install.sh"
    echo 'NIXOS_CONFIG_EOF' >> "${CLOUD_INIT_DIR}/run-install.sh"

    # Continue with installation
    cat >> "${CLOUD_INIT_DIR}/run-install.sh" <<'EOF'

# Install NixOS
echo "Installing NixOS..."
nixos-install --no-root-passwd

echo "=== NixOS Installation Completed Successfully ==="
echo "Rebooting in 5 seconds..."
sleep 5
reboot
EOF

    chmod +x "${CLOUD_INIT_DIR}/run-install.sh"
    log_success "Installation script created"
}

inject_and_run_script() {
    log_info "Injecting and running installation script via console..."
    
    # Create a script that sends commands to the VM console
    cat > "${CLOUD_INIT_DIR}/inject-commands.sh" <<EOF
#!/bin/bash

# Function to send command to VM console
send_to_console() {
    local cmd="\$1"
    echo "Sending: \$cmd"
    echo "\$cmd" | virsh console "$VM_NAME" --force &
    sleep 2
    pkill -f "virsh console $VM_NAME" || true
}

# Send the installation commands one by one
send_to_console "sudo su -"
sleep 2

send_to_console "parted /dev/vda --script -- mklabel msdos"
sleep 3

send_to_console "parted /dev/vda --script -- mkpart primary 1MB -8GB"
sleep 3

send_to_console "parted /dev/vda --script -- mkpart primary linux-swap -8GB 100%"
sleep 3

send_to_console "mkfs.ext4 -L nixos /dev/vda1"
sleep 5

send_to_console "mkswap -L swap /dev/vda2"
sleep 3

send_to_console "mount /dev/disk/by-label/nixos /mnt"
sleep 3

send_to_console "swapon /dev/vda2"
sleep 2

send_to_console "nixos-generate-config --root /mnt"
sleep 5

# Copy configuration
send_to_console "cat > /mnt/etc/nixos/configuration.nix << 'NIXOS_CONFIG_EOF'"
sleep 1
EOF

    # Add the configuration content to the injection script
    cat "${CLOUD_INIT_DIR}/configuration.nix" | while IFS= read -r line; do
        echo "send_to_console \"$line\"" >> "${CLOUD_INIT_DIR}/inject-commands.sh"
        echo "sleep 0.1" >> "${CLOUD_INIT_DIR}/inject-commands.sh"
    done

    cat >> "${CLOUD_INIT_DIR}/inject-commands.sh" <<EOF
send_to_console "NIXOS_CONFIG_EOF"
sleep 2

send_to_console "nixos-install --no-root-passwd"
sleep 2

echo "Installation started. This will take several minutes..."
EOF

    chmod +x "${CLOUD_INIT_DIR}/inject-commands.sh"
}

run_automated_installation() {
    log_info "Starting automated installation process..."
    
    if ! wait_for_vm_ready; then
        log_error "VM not ready for installation"
        return 1
    fi
    
    create_installation_script
    
    log_info "Running installation via direct console commands..."
    
    # Use a simpler approach - create a single command string
    local install_commands
    install_commands=$(cat <<'INSTALL_EOF'
sudo su - && \
parted /dev/vda --script -- mklabel msdos && \
parted /dev/vda --script -- mkpart primary 1MB -8GB && \
parted /dev/vda --script -- mkpart primary linux-swap -8GB 100% && \
mkfs.ext4 -L nixos /dev/vda1 && \
mkswap -L swap /dev/vda2 && \
mount /dev/disk/by-label/nixos /mnt && \
swapon /dev/vda2 && \
nixos-generate-config --root /mnt
INSTALL_EOF
)

    log_info "You can run the installation manually in VNC with these commands:"
    echo "=================================="
    echo "$install_commands"
    echo "=================================="
    echo
    echo "Then copy the configuration:"
    echo "cat > /mnt/etc/nixos/configuration.nix << 'EOF'"
    cat "${CLOUD_INIT_DIR}/configuration.nix"
    echo "EOF"
    echo
    echo "Finally run: nixos-install --no-root-passwd && reboot"
    
    return 0
}

monitor_installation() {
    log_info "Monitoring installation progress..."
    
    local max_wait=1800  # 30 minutes
    local waited=0
    
    while [[ $waited -lt $max_wait ]]; do
        # Check if VM has rebooted and has an IP
        if virsh domifaddr "$VM_NAME" | grep -q "ipv4"; then
            local vm_ip
            vm_ip=$(virsh domifaddr "$VM_NAME" | grep ipv4 | awk '{print $4}' | cut -d'/' -f1)
            log_success "VM has IP address: $vm_ip"
            
            # Try to SSH
            if timeout 5 ssh -o ConnectTimeout=3 -o StrictHostKeyChecking=no nixos@"$vm_ip" echo "SSH test" 2>/dev/null; then
                log_success "Installation complete! SSH is working."
                log_success "You can now SSH to: ssh nixos@$vm_ip"
                return 0
            fi
        fi
        
        sleep 30
        ((waited+=30))
        log_info "Waited ${waited}s / ${max_wait}s for installation to complete..."
    done
    
    log_warn "Installation monitoring timeout"
    return 1
}

main() {
    log_info "Automated NixOS Installation for VM: $VM_NAME"
    
    if [[ ! -f "${CLOUD_INIT_DIR}/configuration.nix" ]]; then
        log_error "Configuration file not found: ${CLOUD_INIT_DIR}/configuration.nix"
        log_error "Run the create-nixos-vm script first"
        exit 1
    fi
    
    case "${2:-auto}" in
        "auto")
            run_automated_installation
            ;;
        "monitor")
            monitor_installation
            ;;
        *)
            echo "Usage: $0 [vm-name] [auto|monitor]"
            echo "  auto    - Run automated installation"
            echo "  monitor - Monitor installation progress"
            ;;
    esac
}

main "$@"
