#!/bin/bash

# Monitor NixOS Installation Progress

VM_NAME="${1:-nixos}"

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() { echo -e "${BLUE}[INFO]${NC} $*"; }
log_success() { echo -e "${GREEN}[SUCCESS]${NC} $*"; }
log_warn() { echo -e "${YELLOW}[WARN]${NC} $*"; }

check_vm_status() {
    log_info "Checking VM status..."
    
    if ! virsh list --all | grep -q "$VM_NAME"; then
        log_warn "VM '$VM_NAME' not found"
        return 1
    fi
    
    local vm_state
    vm_state=$(virsh list --all | grep "$VM_NAME" | awk '{print $3}')
    log_info "VM state: $vm_state"
    
    # Check if VM has IP address
    local vm_ip
    if virsh domifaddr "$VM_NAME" | grep -q "ipv4"; then
        vm_ip=$(virsh domifaddr "$VM_NAME" | grep ipv4 | awk '{print $4}' | cut -d'/' -f1)
        log_success "VM has IP address: $vm_ip"
        
        # Try SSH to see if installation is complete
        if timeout 5 ssh -o ConnectTimeout=3 -o StrictHostKeyChecking=no nixos@"$vm_ip" echo "SSH test" 2>/dev/null; then
            log_success "Installation complete! SSH is working."
            log_success "You can SSH with: ssh nixos@$vm_ip"
            return 0
        else
            log_info "VM has IP but SSH not ready yet (installation may still be running)"
        fi
    else
        log_info "VM has no IP address (likely still installing or booting from ISO)"
    fi
    
    return 1
}

monitor_installation() {
    log_info "Monitoring NixOS installation for VM: $VM_NAME"
    log_info "Press Ctrl+C to stop monitoring"
    
    local check_count=0
    while true; do
        ((check_count++))
        echo
        log_info "Check #$check_count - $(date)"
        
        if check_vm_status; then
            log_success "Installation monitoring complete!"
            break
        fi
        
        log_info "Waiting 30 seconds before next check..."
        sleep 30
    done
}

show_console() {
    log_info "Connecting to VM console (press Ctrl+] to exit)..."
    virsh console "$VM_NAME"
}

case "${2:-monitor}" in
    "monitor")
        monitor_installation
        ;;
    "status")
        check_vm_status
        ;;
    "console")
        show_console
        ;;
    *)
        echo "Usage: $0 [vm-name] [monitor|status|console]"
        echo "  monitor  - Continuously monitor installation progress"
        echo "  status   - Check current status once"
        echo "  console  - Connect to VM console"
        ;;
esac
