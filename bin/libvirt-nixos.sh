#!/bin/bash

set -e # Exit immediately if a command exits with a non-zero status.
# set -x # For debugging

# --- Configuration ---
VM_NAME="ubuntu-docker-vm"
RAM_MB="2048" # 2GB
VCPUS="2"
DISK_SIZE_GB="20"        # Total disk size for the VM
OS_VARIANT="ubuntu22.04" # For virt-install optimization hints

# User for inside the VM (created by cloud-init)
VM_USER="ubuntu"
# Path to your SSH public key to inject into the VM
SSH_PUB_KEY_PATH="$HOME/.ssh/id_rsa.pub"

# Ubuntu Cloud Image URL (Jammy Jellyfish 22.04 LTS - latest as of writing)
# Find latest at: https://cloud-images.ubuntu.com/
CLOUD_IMAGE_URL="https://cloud-images.ubuntu.com/jammy/current/jammy-server-cloudimg-amd64.img"
BASE_IMAGE_DIR="$HOME/virt/images/base" # Directory to store downloaded base cloud image
BASE_IMAGE_NAME=$(basename "$CLOUD_IMAGE_URL")
BASE_IMAGE_PATH="$BASE_IMAGE_DIR/$BASE_IMAGE_NAME"

VM_STORAGE_DIR="/var/lib/libvirt/images" # Standard location for libvirt VM disks
VM_DISK_PATH="${VM_STORAGE_DIR}/${VM_NAME}.qcow2"

USER_DATA_FILE="/tmp/user-data-${VM_NAME}.yaml"

# --- Helper Functions ---
info() {
  echo "[INFO] $1"
}

error() {
  echo "[ERROR] $1" >&2
  exit 1
}

check_command() {
  if ! command -v "$1" &>/dev/null; then
    error "$1 command not found. Please install it."
  fi
}

# --- Prerequisite Checks ---
info "Checking prerequisites..."
check_command "virt-install"
check_command "virsh"
check_command "wget"
check_command "qemu-img"

if [ ! -f "$SSH_PUB_KEY_PATH" ]; then
  error "SSH public key not found at $SSH_PUB_KEY_PATH. Please generate one using 'ssh-keygen'."
fi

# --- Ensure libvirt default network is active ---
info "Ensuring libvirt 'default' network is active..."
if ! virsh net-list --all | grep -q "default\s*active"; then
  if virsh net-list --all | grep -q "default\s*inactive"; then
    info "Starting 'default' network..."
    sudo virsh net-start default
  else
    error "Libvirt 'default' network not found. Please ensure it's defined (often created by virt-manager)."
    # You might need to create it:
    # sudo virsh net-define /usr/share/libvirt/networks/default.xml (path may vary)
    # sudo virsh net-autostart default
    # sudo virsh net-start default
  fi
fi
sudo virsh net-autostart default # Ensure it starts on boot

# --- Prepare Base Cloud Image ---
info "Preparing base cloud image..."
mkdir -p "$BASE_IMAGE_DIR"
if [ ! -f "$BASE_IMAGE_PATH" ]; then
  info "Downloading Ubuntu cloud image from $CLOUD_IMAGE_URL..."
  wget -O "$BASE_IMAGE_PATH" "$CLOUD_IMAGE_URL"
else
  info "Ubuntu cloud image already downloaded: $BASE_IMAGE_PATH"
fi

# --- Prepare VM Disk ---
info "Preparing VM disk image at $VM_DISK_PATH..."
if [ -f "$VM_DISK_PATH" ]; then
  error "VM disk $VM_DISK_PATH already exists. Please remove it or choose a different VM name."
fi

# Create a new qcow2 image for the VM, using the cloud image as a backing file (efficient)
# Or, for a full clone (independent disk), copy and resize:
info "Creating a full clone of the base image for the VM..."
sudo cp "$BASE_IMAGE_PATH" "$VM_DISK_PATH"
info "Resizing VM disk to ${DISK_SIZE_GB}G..."
sudo qemu-img resize "$VM_DISK_PATH" "${DISK_SIZE_GB}G"
sudo chown libvirt-qemu:kvm "$VM_DISK_PATH" # Ensure correct permissions for libvirt
sudo chmod 640 "$VM_DISK_PATH"

# --- Create Cloud-Init User Data ---
info "Creating cloud-init user-data file: $USER_DATA_FILE"
SSH_PUB_KEY_CONTENT=$(cat "$SSH_PUB_KEY_PATH")

cat <<EOF >"$USER_DATA_FILE"
#cloud-config
hostname: ${VM_NAME}
manage_etc_hosts: true
users:
  - name: ${VM_USER}
    sudo: ALL=(ALL) NOPASSWD:ALL
    groups: users, admin, docker
    shell: /bin/bash
    ssh_authorized_keys:
      - ${SSH_PUB_KEY_CONTENT}
package_update: true
package_upgrade: true
packages:
  - qemu-guest-agent
  - curl
  - git # Often useful
runcmd:
  # Install Docker using the official script
  - [ sh, -c, "curl -fsSL https://get.docker.com -o get-docker.sh" ]
  - [ sh, get-docker.sh ]
  # Enable and start Docker service (though get-docker.sh usually does this)
  - [ systemctl, enable, docker ]
  - [ systemctl, start, docker ]
  # Ensure SSH is running and configured
  - [ systemctl, restart, sshd ] # Cloud-init often handles this, but good to be explicit
power_state:
  mode: reboot # Optional: reboot after cloud-init finishes
EOF
info "Cloud-init user-data file created."
# cat "$USER_DATA_FILE" # Uncomment to inspect

# --- Install VM ---
info "Starting VM installation with virt-install..."
sudo virt-install \
  --name "$VM_NAME" \
  --memory "$RAM_MB" \
  --vcpus "$VCPUS" \
  --os-variant "$OS_VARIANT" \
  --disk path="$VM_DISK_PATH",device=disk,bus=virtio,format=qcow2 \
  --import \
  --network network=default,model=virtio \
  --graphics none \
  --console pty,target_type=serial \
  --cloud-init "user-data=$USER_DATA_FILE" \
  --noautoconsole # Don't automatically connect to console, we'll get IP

# --- Wait for VM to get IP and SSH to be ready ---
info "VM installation started. Waiting for cloud-init to complete and VM to get an IP address..."
VM_IP=""
MAX_WAIT_SECONDS=300 # 5 minutes
WAIT_INTERVAL=10     # seconds
SECONDS_WAITED=0

while [ -z "$VM_IP" ] && [ "$SECONDS_WAITED" -lt "$MAX_WAIT_SECONDS" ]; do
  sleep "$WAIT_INTERVAL"
  SECONDS_WAITED=$((SECONDS_WAITED + WAIT_INTERVAL))
  # Try getting IP via qemu-guest-agent first (more reliable if agent is up)
  VM_IP=$(sudo virsh domifaddr --source agent "$VM_NAME" 2>/dev/null | awk '/ipv4/ && $2 ~ /^192\.168\.122\./ {print $4}' | cut -d'/' -f1 | head -n1)

  if [ -z "$VM_IP" ]; then
    # Fallback to DHCP lease (less reliable for exact interface)
    VM_IP=$(sudo virsh domifaddr --source lease "$VM_NAME" 2>/dev/null | awk '/ipv4/ && $2 ~ /^192\.168\.122\./ {print $4}' | cut -d'/' -f1 | head -n1)
  fi

  if [ -n "$VM_IP" ]; then
    info "VM IP Address potentially found: $VM_IP. Verifying SSH..."
    if ssh -o ConnectTimeout=5 -o StrictHostKeyChecking=no -o UserKnownHostsFile=/dev/null "${VM_USER}@${VM_IP}" "echo SSH_OK" 2>/dev/null; then
      info "SSH connection successful to $VM_IP."
      break
    else
      info "SSH not yet ready on $VM_IP. Waiting..."
      VM_IP="" # Clear IP so loop continues
    fi
  else
    info "Still waiting for IP... ($SECONDS_WAITED/$MAX_WAIT_SECONDS s)"
  fi
done

# --- Cleanup Temporary User Data ---
# rm -f "$USER_DATA_FILE" # Comment out if you want to inspect it after run

# --- Output ---
if [ -n "$VM_IP" ]; then
  info "------------------------------------------------------------"
  info "VM '$VM_NAME' is up and running!"
  info "IP Address: $VM_IP"
  info "You can SSH into the VM using:"
  info "  ssh ${VM_USER}@${VM_IP}"
  info "To connect to the VM console (Ctrl + ] to exit):"
  info "  sudo virsh console $VM_NAME"
  info "To check Docker version inside VM:"
  info "  ssh ${VM_USER}@${VM_IP} \"docker --version\""
  info "------------------------------------------------------------"
else
  error "Failed to get VM IP address after $MAX_WAIT_SECONDS seconds."
  info "The VM might still be booting or cloud-init might be taking longer."
  info "You can check its status with 'sudo virsh list' and 'sudo virsh console $VM_NAME'."
fi

exit 0
