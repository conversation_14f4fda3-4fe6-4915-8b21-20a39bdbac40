#! /usr/bin/env bash

if is_linux; then
    alias upgrade="sudo apt-get -qq update && sudo apt-get -qq -y upgrade"
    alias open="xdg-open"

    pbcopy() {
        if [ "$XDG_SESSION_TYPE" = "wayland" ]; then
            wl-copy
        else
            xsel --clipboard --input
        fi
    }

    pbpaste() {
        if [ "$XDG_SESSION_TYPE" = "wayland" ]; then
            wl-paste --no-newline
        else
            xsel --clipboard --output
        fi
    }
fi

if is_mac; then
    alias upgrade="brew update && brew upgrade"
fi

alias xen="TERM=xterm-256color emacs -nw"
alias xet="TERM=xterm-256color emacsclient -t"

alias ten= "TERM=xterm-24bits emacs -nw"
alias tet="TERM=xterm-24bits emacsclient -t"

alias emacs-kill="seartipy_kill_emacs"

alias m="mkdir -p"

if has_cmd code; then
    alias c='code'
    alias c.='code .'
    alias cs="code --password-store=gnome-libsecret"
    alias dcode="XDG_CURRENT_DESKTOP=GNOME code" # distrobox code in kde
fi

if has_cmd pnpm; then
    alias n='pnpm'
    alias ni='pnpm install'
    alias ne='pnpm exec'
    alias nd='pnpm dev'
    alias nt='pnpm types'
    alias nc='pnpm ci'
    alias ntt='pnpm test'
fi

sup() {
    if is_ubuntu; then
        sudo apt-get -qq update && sudo apt-get -qq -y upgrade
    elif is_atomic; then
        sudo rpm-ostree upgrade
    elif is_rh; then
        sudo dnf update -q -y && sudo dnf upgrade -q -y
    elif is_tw; then
        sudo zypper refresh && sudo zypper --non-interactive --quiet dup
    elif is_mac; then
        brew update && brew upgrade
    elif is_arch; then
        sudo pacman -Syu --noconfirm --quiet
    else
        echo "Unknown OS. Cannot upgrade."
    fi
}

if is_ubuntu; then
    alias si="sudo apt-get -qq -y install"
    alias ss="apt-cache search"
    alias sr="sudo apt-get purge"
elif is_rh; then
    alias si="sudo dnf -q -y install"
    alias ss="dnf search"
    alias sr="sudo dnf remove"
elif is_tw; then
    alias si="sudo zypper --non-interactive --quiet install --auto-agree-with-licenses"
    alias ss="zypper search"
    alias sr="sudo zypper remove"
elif is_mac; then
    alias si="brew install -q"
    alias ss="brew search"
    alias sr="brew uninstall"
elif is_arch; then
    alias si="sudo pacman -S --needed --quiet --noconfirm"
    alias ss="pacman -Ss"
    alias sr="sudo pacman -R"
fi

if has_cmd git; then
    alias git-unstage="git reset HEAD"
    alias git-discard="git checkout --"
    alias gst="git status"
    alias gsu="git status -u"
    alias tsgfm="git stash && ((git pull --rebase || git pull); git stash pop)"

    alias gun="git-unstage"
    alias gur="git-discard"
    alias gcm="git commit -m"
    alias gcne="git commit --no-edit"
    alias gca="git commit --amend"
    alias gcan="git commit --amend --no-edit"

    alias Gcm="git commit --no-verify -m"
    alias Gp="gp --no-verify"
    alias Gcan="gcan --no-verify"

    alias g='git'
    alias gs="git stash -u"
    alias gst="git status"
    alias gsu="git status -u"
    alias gcan="git commit --amend --no-edit"
    alias gsa='git stash apply'
    alias gfm='git pull'
    alias gp='git push'
    alias gcm='git commit --message'
    alias gia='git add'
    alias gl='git log --topo-order --pretty=format:"%C(yellow)%h%C(reset)%C(black)%d%C(reset) %C(cyan)%ar%C(reset) %C(green)%an%C(reset)%n%C(white)%s%C(reset)"'
    alias gco='git checkout'
    alias gb='git branch'
    alias gbc='git checkout -b'
    alias gbc='git checkout -b'
    alias gsl='git stash list'
    gpp() {
        safe_push "$@"
    }
    alias clone='gh repo clone'
    alias glog="git log --graph --topo-order --pretty='%w(100,0,6)%C(yellow)%h%C(bold)%C(black)%d %C(cyan)%ar %C(green)%an%n%C(bold)%C(white)%s %N' --abbrev-commit"
fi

has_cmd nmap && alias nm="nmap -sC -sV -oN"

has_cmd nvim && alias v="nvim"
has_cmd code && alias c="code"
has_cmd tmux && alias t="tmux"

if has_cmd docker; then
    alias d="docker"
    alias dco="docker compose"
    alias dps="docker ps"
    alias dpa="docker ps -a"
    alias dl="docker ps -l -q"
    alias dx="docker exec -it"
    alias dlogs="docker logs -f"
    alias lzg='lazygit'
    alias lzd='lazydocker'
fi

if has_cmd distrobox; then
    alias dbox='distrobox'
    alias dboxe='distrobox enter -nw --clean-path'
    alias dboxl='distrobox list'
    alias dboxr='distrobox run'
    alias dboxc='distrobox create'
    alias dboxd='distrobox rm'

    alias rdboxe='distrobox enter -nw --clean-path --root'
    alias rdboxl='distrobox list --root'
    alias rdboxr='distrobox run --root'
    alias rdboxc='distrobox create --root'
    alias rdboxd='distrobox rm --root'

    [[ "${CONTAINER_ID}" != "" ]] && alias on-host='distrobox-host-exec'
fi

if has_cmd fastfetch; then
    alias sys-info='fastfetch'
elif has_cmd neofetch; then
    alias sys-info='neofetch'
fi

if has_cmd toolbox; then
    alias tbox='toolbox'
    alias tboxe='toolbox enter'
    alias tboxl='toolbox list'
    alias tboxr='toolbox run'
    alias tboxc='toolbox create'
fi

alias la=tree
alias lm='la | "$PAGER"' # Lists human readable sizes, hidden files through pager.
alias lr='ll -R'         # Lists human readable sizes, recursively.
alias lc='lt -c'         # Lists sorted by date, most recent last, shows change time.
alias lu='lt -u'         # Lists sorted by date, most recent last, shows access time.
alias sl='ls'            # Correction for common spelling error.

alias md="mkdir -p"
alias rd="rmdir"
alias cpr="cp -r"
alias md="mkdir -p"
alias rd="rmdir"
alias del="trash"
alias ipa="ip -brief a"

has_cmd gdu || has_cmd gdu-go && alias gdu="gdu-go"

alias ..="../"
alias ...="../../"
alias ....="../../../"
alias .....="../../../../"
alias ......="../../../../../"

if [[ ! -v CONTAINER_ID ]] && has_cmd flatpak; then
    FLATPAK_LIST=$(flatpak list)

    alias_if_exists() {
        local cmd="$1"
        local flatpak_id="$2"

        if ! has_cmd "$cmd"; then
            if echo "$FLATPAK_LIST" | grep -q "$flatpak_id"; then
                eval "alias $cmd='flatpak run $flatpak_id'"
            fi
        fi
    }

    alias_if_exists wezterm "org.wezfurlong.wezterm"
    alias_if_exists clion "com.jetbrains.CLion"
    alias_if_exists chrome "com.google.Chrome"
    alias_if_exists code "com.visualstudio.code --password-store=gnome-libsecret"
    alias_if_exists nvim "io.neovim.nvim"
    alias_if_exists emacs "org.gnu.emacs"

    unset FLATPAK_LIST
    unset -f alias_if_exists
fi

if [[ -d "$HOME/.local/share/pnpm" ]]; then
    alias n='pnpm'
    alias ni='pnpm install'
    alias nid='pnpm install -D'
    alias nb='pnpm build'
    alias nl='pnpm lint:dev'
    alias ne='pnpm exec'
    alias nd='pnpm dev'
    alias nc='pnpm ci'
    alias nt='pnpm types:dev'
    alias ntc='pnpm types'
    alias ntt='pnpm test:dev'
    alias nttc='pnpm test:dev'
    alias nci='pnpm types && pnpm lint'
    alias ndb='pnpm db'
    alias ndbt='pnpm db:types'
    alias ndbp='pnpm db:push'
    alias ndbs='pnpm db:seed'
    alias ndbst='pnpm db:studio'
    alias ndbr='pnpm db:repl'
fi

if has_cmd eza; then
    alias ls="eza -a --icons=auto --group-directories-first"
    alias ll='eza -l --icons=auto --group-directories-first'
    alias l1='eza -1'
    alias l="eza -l --icons --git -a"
    alias lt="eza --tree --level=2 --long --icons --git"
    alias ltree="eza --tree --level=2  --icons --git"
    # alias ls='eza -lh --group-directories-first --icons'

    alias lt='eza --tree --level=2 --long --icons --git'
    alias l.='eza -d .*'
else
    alias l='ls -1A'  # Lists in one column, hidden files.
    alias ll='ls -lh' # Lists human readable sizes.
    alias lk='ll -Sr' # Lists sorted by size, largest last.
    alias lt='ll -tr' # Lists sorted by date, most recent last.
fi

# ugrep for grep
if has_cmd ug; then
    alias grep='ug'
    alias egrep='ug -E'
    alias fgrep='ug -F'
    alias xzgrep='ug -z'
    alias xzegrep='ug -zE'
    alias xzfgrep='ug -zF'
fi

if has_cmd lazyvim && has_cmd nvim; then
    alias lv="NVIM_APPNAME=lazyvim nvim"
fi

alias lsa='ls -a'
alias lta='lt -a'

if has_cmd fzf; then
    alias ff="fzf --preview 'bat --style=numbers --color=always {}'"
    alias fv="fzf --bind 'enter:become(vim {})'"
fi

# function c() {
#     code --enable-features=UseOzonePlatform --ozone-platform-hint=auto "$@"
# }

# function ci() {
#     code-insiders --enable-features=UseOzonePlatform --ozone-platform-hint=auto "$@"
# }

fcd() {
    cd "$(find . -type d -not -path '*/.*' | fzf)" && l
}

fvim() {
    local file
    file=$(find . -type f -not -path '*/.*' | fzf)
    if [ -n "$file" ]; then
        nvim "$file"
    fi
}

if has_cmd emacs; then
    # export EDITOR="emacsclient -t"
    # export VISUAL="emacsclient -c -n"
    alias e="emacsclient -t"
    alias ec="emacsclient -c -n"
    alias en="emacs -nw"
fi

if has_cmd bat; then
    alias cat="bat"
elif has_cmd batcat; then
    alias cat="batcat"
fi
