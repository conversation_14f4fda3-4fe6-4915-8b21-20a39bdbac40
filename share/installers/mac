#! /usr/bin/env bash

macos_settings_install() {
    defaults write -g NSWindowShouldDragOnGesture -bool true
}

amethyst_config_install() {
    slog "amethyst config"
    stowdf amethyst
    slog "amethyst config done!"

}

aerospace_config_install() {
    slog "aerospace config"
    stowgf aerospace
}

macos_config_install() {
    stow -d "$DOT_DIR" -t "$HOME" --dotfiles -R aerospace
    stow -d "$DOT_DIR" -t "$HOME" --dotfiles -R amethyst
}

vscode_bin_install() {
    bi visual-studio-code
}

terminal_bin_install() {
    bic ghostty
}

ui_install() {
    terminal_bin_install
    vscode_bin_install

    bi deluge
    bic telegram zoom google-chrome microsoft-remote-desktop
    bic nikitabobko/tap/aerospace
}

core_install() {
    slog "Install xcode"
    xcode-select --install
    brew_install

    bi mas coreutils bash curl wget trash tree unzip coreutils gum stow
}

essential_install() {
    slog "Installing Essential packages"

    bis gawk p7zip cmake unar zip

    slog "Essential packages installation done!"
}

cli_install() {
    bis tmux pkg-config urlview htop starship gping broot mcfly jq \
        zsh-syntax-highlighting zsh-autosuggestions golang rust ripgrep neovim \
        neovide luarocks lazydocker reattach-to-user-namespace \
        brew link luarocks
}

fonts_install() {
    bic font-jetbrains-mono-nerd-font font-monaspace-nerd-font font-caskaydia-mono-nerd-font
}

cpp_install() {
    slog "Installing C++"

    bi cmake boost catch2 ccache cppcheck pre-commit

    slog "C++ installation done!"
}

ct_install() {
    softwareupdate --install-rosetta --agree-to-license
    slog "Installing Container tools"

    bi podman
    bic podman-desktop
    podman machine init
    podman machine start
    dir_exists /Applications/Docker.app || bic docker

    slog "Container tools installation done!"
}

vm_install() {
    slog "Installing virtualization packages"

    bi orbstack colima lim

    slog "Virtualization packages installation done!"
}

pyenv_mac_install() {
    slog "Installing pyenv"
    bi pyenv pyenv-virtualenv
}

emacs_bin_install() {
    dir_exists /Applications/Emacs.app && return 0
    dir_exists /usr/local/opt/emacs-mac && return 0

    slog "Installing emacs"

    bic emacs-mac

    slog "emacs installation done!"
}

docker_install() {
    has_cmd docker && return 0
    dir_exists /Applications/Docker.app && return 0

    slog "Installing docker"

    if [ -f ~/Downloads/Docker.dmg ]; then
        warn "Docker.dmg already exists, skipping download"
    else
        curl -sSL https://desktop.docker.com/mac/main/amd64/Docker.dmg -o ~/Downloads/Docker.dmg
    fi

    if [ -d /Volumes/Docker ]; then
        warn "Docker.dmg already mounted, unmounting"
        sudo hdiutil detach /Volumes/Docker
    fi
    sudo hdiutil attach ~/Downloads/Docker.dmg
    sudo /Volumes/Docker/Docker.app/Contents/MacOS/install
    sudo hdiutil detach /Volumes/Docker

    slog "docker installation done!"
}
