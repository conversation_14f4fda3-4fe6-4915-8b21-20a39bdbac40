#! /usr/bin/env bash

# shellcheck disable=SC1090
source <(curl -sSL https://raw.githubusercontent.com/pervezfunctor/dotfiles/refs/heads/main/share/utils) || err_exit "Cannot source utils, Quitting"

ublue_brew_setup() {
    if ! has_cmd brew; then
        warn "brew not installed; skipping shell utilities installation."
        return 1
    fi

    brew_shell_slim_install
}

ublue_emacs_setup() {
    slog "emacs installation and setup"

    bis emacs
    bi emacs-clang-complete-async
    srm ~/.emacs
    srm ~/.emacs.d
    srm "$XDG_CONFIG_HOME/emacs"
    stowdf emacs-slim # or emacs

    slog "emacs installation done!"
}

ublue_cpp_setup() {
    slog "Installing C++ tools"

    bi llvm lld
    bis clang-format cmake

    slog "C++ installation done!"
}

ublue_apps_setup() {
    if ! has_cmd flatpak; then
        warn "flatpak not installed; skipping apps installation"
        return 1
    fi

    slog "Installing apps for ublue"
    apps_slim_install
    slog "apps installation done!"
}

ublue_code_setup() {
    slog "code setup"

    if ! has_cmd code; then
        warn "code not installed; skipping vscode configuration"
        return 1
    fi

    vscode-extensions_install
    vscode_config_install

    slog "code setup done!"
}

config_setup() {
    if ! dir_exists "$DOT_DIR"; then
        warn "$DOT_DIR doesn't exist, skipping configuration"
        return 1
    fi

    if ! has_cmd stow; then
        warn "stow not installed, skipping configuration"
        return 1
    fi

    slog "config setup"

    bash_config_install
    zsh_config_install
    tmux_config_install

    slog "config setup done!"
}

bluefin_setup() {
    slog "gnome setup"

    gnome_settings_install
    # gnome_keybindings_install
    gnome_extensions_install

    slog "gnome setup done!"
}

main() {
    dotfiles_install
    python_install
    ublue_brew_setup
    ublue_apps_setup
    ublue_code_setup

    config_setup
    is_bluefin && bluefin_setup

    if [[ "$USER" == "pervez" ]]; then
        slog "Setting up ublue for myself"
        ublue_cpp_setup
        ublue_emacs_setup
        nvim_config_install
        git_config_install
    fi
}

if is_ublue && has_cmd git && has_cmd brew && has_cmd python3 && has_cmd code && has_cmd flatpak && has_cmd distrobox; then
    export NOSUDO=1
    bootstrap "uBlue OS"
else
    err_exit "Prerequisites not met. You need Aurora/Bluefin with git, just, brew, code, flatpak, distrobox and python3 installed."
fi
