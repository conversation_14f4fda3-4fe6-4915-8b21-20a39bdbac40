#! /usr/bin/env bash

zi() {
    sudo zypper --non-interactive --quiet install --auto-agree-with-licenses "$@"
}

si() {
    zi "$@" && return 0

    for p in "$@"; do
        slog "Installing package $p"
        zi "$p"
    done
}

update_packages() {
    slog "Updating SUSE"

    if ! { sudo zypper refresh && sudo zypper --non-interactive --quiet dup; }; then
        err_exit "zypper refresh failed. quitting"
    fi
}

tw_packages() {
    update_packages

    slog "Installing packages"

    si git gh git-delta unzip wget curl trash-cli tar stow gcc make file \
        starship gum wl-clipboard tree bat eza fzf ripgrep zoxide fd \
        htop sd tealdeer yazi cheat lazygit libsecret

    slog "Installing packages done!"
}

sys_python_install() {
    slog "Installing python"

    si python313 python313-virtualenv python313-pip python313-setuptools \
        python313-pipx
    pipx install uv

    slog "Python installation done!"
}

core_install() {
    update_packages

    slog "Installing core packages"

    si curl wget git tar tree unzip cmake stow file trash-cli libsecret
    sudo zypper install -y -t pattern devel_basis

    slog "Core packages installation done!"
}

snap_install() {
    has_cmd snap && return 0

    slog "Installing snap..."

    sudo zypper addrepo --refresh https://download.opensuse.org/repositories/system:/snappy/openSUSE_Tumbleweed snappy
    sudo zypper --gpg-auto-import-keys refresh
    sudo zypper dup --from snappy
    sudo systemctl enable --now snapd

    slog "snap installation done!"
}

essential_install() {
    sys_python_install
    # snap_install

    slog "Installing essential packages"
    si unar zip micro-editor 7zip gawk readline-devel sqlite3-devel \
        libffi-devel libbz2-devel xz-devel gum

    slog "Essential packages installation done!"
}

cli_install() {
    slog "Installing cli tools using zypper"

    si gh neovim zsh tmux sd bat eza fd fzf ripgrep zoxide just procs \
        lazygit bottom git-delta htop xh plocate tealdeer urlview nushell \
        lua54-luarocks python3-neovim ImageMagick gdu duf ugrep yazi dysk \
        shfmt hyperfine cheat curlie jq ShellCheck gum lsd starship direnv yq

    slog "cli tools installation done!"
}

cpp_install() {
    slog "Installing C++"

    si gcc gdb g++ boost-devel catch2-devel ltrace strace lldb lld \
        clang llvm clang-tools clang-devel valgrind systemtap \
        doxygen graphviz ccache cppcheck python3-pre-commit

    if has_cmd brew; then
        bi cmake
    else
        cmake_install
    fi

    conan_install

    slog "C++ installation done!"
}

vscode_bin_install() {
    has_cmd code && return 0

    slog "Installing vscode"

    sudo rpm --import https://packages.microsoft.com/keys/microsoft.asc
    sudo sh -c 'echo -e "[code]\nname=Visual Studio Code\nbaseurl=https://packages.microsoft.com/yumrepos/vscode\nenabled=1\ntype=rpm-md\ngpgcheck=1\ngpgkey=https://packages.microsoft.com/keys/microsoft.asc" > /etc/zypp/repos.d/vscode.repo'
    sudo zypper refresh
    si code

    slog "vscode installation done!"

    cmd_check code
}

terminal_bin_install() {
    has_cmd ghostty && return 1

    slog "Installing ghostty"
    si ghostty
    slog "ghostty installation done!"
}

ui_install() {
    slog "Installing ui"

    si chromium-browser gnome-keyring wl-clipboard flatpak

    flathub_install

    terminal_bin_install
    vscode_bin_install
    apps_install

    has_cmd virt-install && si virt-manager virt-viewer

    slog "ui installation done!"
}

cockpit_install() {
    has_cmd cockpit && return 1

    slog "Installing cockpit"

    si systemd-networkd cockpit cockpit-machines cockpit-pcp cockpit-podman \
        cockpit-storaged cockpit-kdump cockpit-networkmanager \
        cockpit-packagekit cockpit-system cockpit-tukit
    # microos_cockpit patterns-microos-cockpit
    sudo systemctl enable --now cockpit.socket

    slog "cockpit installation done!"
}

podman_install() {
    has_cmd podman && return 1

    slog "Installing podman"
    si podman podman-remote
    slog "podman installation done!"
}

vm_install() {
    si libvirt qemu-kvm virt-install bridge-utils virglrenderer

    sudo systemctl enable --now libvirtd
}

docker_install() {
    has_cmd docker && return 1

    slog "Installing docker"

    si docker docker-compose docker-compose-switch
    docker_config_install

    slog "docker installation done!"
}

incus_install() {
    has_cmd incus && return 1

    slog "Installing incus"

    si incus
    incus_config_install

    slog "incus installation done!"
}

ct_install() {
    slog "Installing container tools"

    incus_install
    docker_install
    podman_install
    cockpit_install

    si buildah distrobox

    slog "Container tools installation done!"
}

virt_install() {
    slog "Installing virtualization packages"

    vm_install
    ct_install

    code_server_install

    slog "Virtualization packages installation done!"
}

sway_install() {
    slog "Installing sway"

    sudo zypper install -y -t pattern openSuseway

    si openSuseway qt6ct alacritty swaylock swaybg swayidle \
        grim clipman imv mpv bluez slirp qt5ct swaynag waybar \
        brightnessctl pamixer playerctl greetd mpris-ctl firefox \
        symbols-only-nerd-fonts jetbrains-mono-fonts wl-clipboard \
        rofi-wayland SwayNotificationCenter polkit-gnome slurp \
        SwayNotificationCenter-bash-completion bluemoon tlp \
        SwayNotificationCenter-zsh-completion qt6ct sway-marker

    nerd_fonts_install

    cmd_check sway swaylock swaybg swayidle swaymsg sway-marker
    cmd_check swaynag pactl waybar grim clipman imv mpv
    cmd_check brightnessctl pamixer playerctl greetd alacritty
    cmd_check /usr/libexec/polkit-gnome-authentication-agent-1
    cmd_check bluetoothctl bluemoon tlp firefox slurp qt5ct
    cmd_check swaync swaync-client

    slog "sway installation done!"
}

hyprland_install() {
    slog "Installing hyprland"

    si grep aaa_base bash-completion ghostscript-fonts-std NetworkManager curl \
        tar pipewire wget xdg-utils sudo xdg-desktop-portal \
        xdg-desktop-portal-gtk git gzip bzip2 jq adwaita-icon-theme \
        dejavu-fonts glibc-locale less wl-clipboard google-roboto-fonts \
        noto-sans-fonts google-droid-fonts google-opensans-fonts \
        gtk3-metatheme-adwaita noto-coloremoji-fonts noto-emoji-fonts \
        adobe-sourcecodepro-fonts command-not-found greetd mpris-ctl \
        metatheme-adwaita-common xdg-desktop-portal-wlr \
        adobe-sourcesanspro-fonts adobe-sourceserifpro-fonts cantarell-fonts \
        ghostscript-fonts-other google-carlito-fonts

    si hyprcursor hypridle hyprland hyprland-bash-completion hyprland-devel \
        hyprlock hyprland-wallpapers hyprland-zsh-completion hyprpaper \
        hyprpicker hyprpolkitagent hyprshot xdg-desktop-portal-hyprland \
        hyprland-qt-support hyprland-qtutils qt6ctwaybar imv mpv \
        clipman bluez slirp qt5ct qt6ct ghostty firefox \
        brightnessctl pamixer playerctl greetd bluemoon tlp slurp mpris-ctl \
        symbols-only-nerd-fonts jetbrains-mono-fonts wl-clipboard rofi-wayland \
        SwayNotificationCenter SwayNotificationCenter-bash-completion \
        SwayNotificationCenter-zsh-completion

    nerd_fonts_install

    cmd_check hyprcursor hypridle hyprctl hyprlock hyproiccker
    cmd_check pactl waybar clipman imv mpv hyprshot ghostty
    cmd_check brightnessctl pamixer playerctl greetd
    # cmd_check /usr/libexec/polkit-gnome-authentication-agent-1
    cmd_check bluetoothctl bluemoon tlp firefox slurp qt5ct
    cmd_check swaync swaync-client

    slog "hyprland installation done!"
}
