#! /usr/bin/env bash

if command -v curl &>/dev/null; then
    # shellcheck disable=SC1090
    source <(curl -sSL https://raw.githubusercontent.com/pervezfunctor/dotfiles/refs/heads/main/share/utils)
elif command -v wget &>/dev/null; then
    # shellcheck disable=SC1090
    source <(wget -qO- https://raw.githubusercontent.com/pervezfunctor/dotfiles/refs/heads/main/share/utils)
else
    echo "curl or wget is not installed"
fi

main() {
    if [[ "$#" -eq 1 ]] && has_cmd "${1}_groupstall"; then
        "${1}_groupstall"
    else
        if has_cmd base_groupstall; then
            base_groupstall
        else
            err_exit "unknown or unsupported $* options"
        fi

        common-installer "$@"
    fi

    has_cmd zh && has_cmd set_zsh_as_default && set_zsh_as_default
}

if [[ "$1" == "generic" ]] || [[ "$1" == "generic-ct" ]] || [[ "$1" == "fedora-atomic" ]] || is_multipass; then
    export NOSUDO=1
fi

bootstrap "$@"

slog "Installation done! Once you reboot you can use 'ilm-group-installer' command to install more packages"
