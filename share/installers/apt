#! /usr/bin/env bash

export DEBIAN_FRONTEND=noninteractive

function si() {
    local found=()
    local not_found=()

    for pkg in "$@"; do
        if apt-cache show "$pkg" >/dev/null 2>&1; then
            found+=("$pkg")
        else
            not_found+=("$pkg")
        fi
    done

    sudo apt-get -qq -y install "${found[@]}"

    for pkg in "${not_found[@]}"; do
        warn "Package $pkg not found in apt repository"
    done

    # for p in "$@"; do
    #     slog "Installing package $p"
    #     sudo apt-get -qq -y install "$p"
    # done
}

pacstall_install() {
    has_cmd pacstall && return 1

    sudo bash -c "$(curl -fsSL https://pacstall.dev/q/install)"
}

update_packages() {
    slog "Updating Ubuntu"

    if ! { sudo apt-get -qq update && sudo apt-get -qq upgrade -y; }; then
        err_exit "apt-get update/upgrade failed, quitting"
    fi
}

ubuntu_packages() {
    update_packages

    slog "Installing packages"

    si git-core gh git-delta unzip wget curl \
        trash-cli tar stow gcc make file wl-clipboard tree bat eza ripgrep \
        zoxide fd-find htop sd bat gawk starship libsecret-tools

    is_plucky && si fzf tealdeer gum

    slog "Installing packages done!"

    pixi_install

    slog "Installing pixi packages"
    pi fzf lazygit yazi tealdeer
    slog "Installing pixi packages done!"
}

debian_packages() {
    update_packages

    slog "Installing packages"
    #  git-delta eza just fzf
    si git-core gh unzip wget curl trash-cli \
        tar stow gcc make file tree bat ripgrep zoxide fd-find htop sd gawk \
        wl-clipboard libsecret-tools

    slog "Installing packages done!"

    pixi_install
    pi fzf yazi tealdeer git-delta eza bat starship lazygit
    slog "Installing pixi packages done!"
}

core_install() {
    update_packages

    slog "Installing core packages"

    si software-properties-common apt-transport-https ca-certificates gpg curl \
        wget git-core trash-cli tar unzip libsecret-tools \
        tree command-not-found file build-essential libfuse2 zstd

    is_plucky && si stow

    slog "Core packages installation done!"
}

sys_python_install() {
    slog "Installing python"

    si python3 python3-venv python3-virtualenv python3-pip python3-setuptools \
        python3-wheel python-is-python3 pipx

    pipx install uv

    slog "Python installation done!"

    cmd_check uv
}

snap_install() {
    has_cmd snap && return 0

    slog "Installing snap..."
    si snapd
    sudo systemctl enable --now snapd.socket
    sudo systemctl enable --now snapd

    sudo snap install core
    slog "snap installation done!"
}

essential_install() {
    slog "Installing essential packages"

    sys_python_install
    snap_install

    si zip unar micro nala p7zip gawk libreadline-dev libsqlite3-dev \
        libffi-dev libbz2-dev liblzma-dev

    slog "Essential packages installation done!"
}

# sudo apt-get -qq -y install \
#   build-essential pkg-config autoconf bison clang rustc \
#   libssl-dev libreadline-dev zlib1g-dev libyaml-dev libreadline-dev libncurses5-dev libffi-dev libgdbm-dev libjemalloc2 \
#   libvips imagemagick libmagickwand-dev mupdf mupdf-tools gir1.2-gtop-2.0 gir1.2-clutter-1.0 \
#   redis-tools sqlite3 libsqlite3-0 libmysqlclient-dev libpq-dev postgresql-client postgresql-client-common wl-clipboard

cli_install() {
    slog "Installing cli tools using apt"

    local base_pkgs=(tmux pkg-config urlview zsh git plocate sd gdu bat hyperfine fd-find
        ripgrep zoxide eza gh shellcheck just htop shfmt)

    local plucky_pkgs=()
    is_plucky && plucky_pkgs=(fzf ugrep tealdeer starship gum git-delta direnv yq)

    si "${base_pkgs[@]}" "${plucky_pkgs[@]}"

    slog "cli tools installation done!"
}

cpp_install() {
    slog "Installing C++"

    si gcc gdb g++ libboost-all-dev catch2 clang llvm clang-tidy clang-format \
        clang-tools libclang-dev clangd doxygen graphviz ccache cppcheck \
        pre-commit valgrind systemtap ltrace strace lldb lld

    if has_cmd pixi; then
        pis cmake
    elif has_cmd brew; then
        bis cmake
    else
        cmake_install
    fi
    conan_install

    cmd_check gcc g++ gdb clang clang-tidy clang-format
    cmd_check cmake conan

    slog "C++ installation done!"
}

vscode_bin_install() {
    has_cmd code && return 0

    slog "Installing vscode"

    if ! grep -q "https://packages.microsoft.com/repos/code stable main" /etc/apt/sources.list.d/vscode.list 2>/dev/null; then
        echo "code code/add-microsoft-repo boolean true" | sudo debconf-set-selections

        wget -qO- https://packages.microsoft.com/keys/microsoft.asc | gpg --dearmor >packages.microsoft.gpg
        sudo install -D -o root -g root -m 644 packages.microsoft.gpg /etc/apt/keyrings/packages.microsoft.gpg

        echo "deb [arch=amd64,arm64,armhf signed-by=/etc/apt/keyrings/packages.microsoft.gpg] https://packages.microsoft.com/repos/code stable main" | sudo tee /etc/apt/sources.list.d/vscode.list >/dev/null

        rm -f packages.microsoft.gpg
    fi

    sudo apt-get -qq update
    si code

    slog "vscode installation done!"

    cmd_check code
}

windsurf_install() {
    has_cmd windsurf && return 0

    slog "Installing windsurf"

    # first ceck if it's already added
    if ! [ -f /etc/apt/sources.list.d/windsurf.list ]; then
        curl -fsSL "https://windsurf-stable.codeiumdata.com/wVxQEIWkwPUEAGf3/windsurf.gpg" | sudo gpg --dearmor -o /usr/share/keyrings/windsurf-stable-archive-keyring.gpg
        echo "deb [signed-by=/usr/share/keyrings/windsurf-stable-archive-keyring.gpg arch=amd64] https://windsurf-stable.codeiumdata.com/wVxQEIWkwPUEAGf3/apt stable main" | sudo tee /etc/apt/sources.list.d/windsurf.list >/dev/null
    fi

    sudo apt-get -qq update
    si windsurf

    slog "windsurf installation done!"
}

terminal_bin_install() {
    has_cmd ghostty && return 1

    slog "Installing terminal"

    if has_cmd snap; then
        snap install ghostty --classic
    else
        si kitty
    fi

    slog "terminal installation done!"
}

vm_ui_install() {

    is_desktop || return 0

    fpi org.gnome.Boxes

    has_cmd virt-install && si virt-manager virt-viewer
}

ui_install() {
    slog "Installing ui"
    si chromium-browser gnome-keyring wl-clipboard flatpak

    flathub_install

    terminal_bin_install
    vscode_bin_install
    apps_install

    vm_ui_install
    slog "ui installation done!"
}

cockpit_install() {
    has_cmd cockpit && return 1

    slog "Installing cockpit"

    si cockpit cockpit-machines cockpit-networkmanager cockpit-packagekit \
        cockpit-sosreport cockpit-pcp cockpit-podman cockpit-storaged \
        cockpit-system

    sudo systemctl enable --now cockpit.socket

    slog "cockpit installation done!"
}

podman_install() {
    slog "Installing podman"
    si podman podman-toolbox podman-compose
    slog "podman installation done!"
}

vm_install() {
    si libvirt-daemon-system qemu-kvm virtinst bridge-utils virglrenderer

    libcirt_config_install
}

incus_install() {
    has_cmd incus && return 1

    slog "Installing incus"

    si incus
    incus_config_install

    slog "incus installation done!"
}

ct_install() {
    slog "Installing container tools"

    incus_install
    docker_install
    portainer_install
    podman_install
    cockpit_install

    si buildah distrobox

    slog "Container tools installation done!"
}

virt_install() {
    slog "Installing virtualization packages"

    ct_install
    vm_install

    code_server_install

    slog "Virtualization packages installation done!"
}
