#! /usr/bin/env bash

if command -v curl >/dev/null || command -v wget >/dev/null; then
    # shellcheck disable=SC1090
    source <(curl -sSL https://raw.githubusercontent.com/pervezfunctor/dotfiles/refs/heads/main/share/utils || wget -qO- https://raw.githubusercontent.com/pervezfunctor/dotfiles/refs/heads/main/share/utils)
else
    echo "curl or wget is not installed"
fi

is_linux || err_exit "This script is only for Linux"

nix_install

has_cmd nix >/dev/null || err_exit "nix not installed! Qutting."

slog "Cloning dotfiles to ~/.ilm"
nix run nkxpkgs#gh -- rep clone https://github.com/pervezfunctor/dotfiles.git ~/.ilm

slog "Setting up home-manager"
hms

is_desktop || return 0

# smd ~/.local/share/fonts
# jetbrains_nerd_font_install
# kitty_bin_install
# kitty_config_install
# vscode_flatpak_install
