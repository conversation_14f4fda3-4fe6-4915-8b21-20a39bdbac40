#! /usr/bin/env bash

nvim_boxstall() {
    srm ~/.config/nvim
    ln -s "$DOT_DIR/nvim/dot-config/nvim" ~/.config/nvim

    has_cmd nvim && return 0

    if has_cmd brew; then
        bi neovim luarocks lazygit
    elif has_cmd pixi; then
        pi nvim luarocks lazygit
    else
        # has_cmd apk && si nvim
        si neovim
        si luarocks
        si lazygit
        is_apt && warn "Older version of neovim is installed. Some features might not work."
    fi

    has_cmd nvim || warn "nvim not installed!"
}

emacs_version() {
    emacs --version | head -1 | cut -d' ' -f3 | cut -d'.' -f1
}

emacs_boxstall() {
    srm ~/.emacs
    srm ~/.emacs.d
    ln -s "$DOT_DIR/emacs-slim/dot-emacs" ~/.emacs

    has_cmd emacs && [ "$(emacs_version)" -gt 29 ] && return 0

    if has_cmd brew; then
        bi emacs
    elif has_cmd pixi; then
        pi emacs
    else
        si emacs-nox
        has_cmd emacs || si emacs
    fi

    has_cmd emacs || warn "emacs not installed! Qutting."
}

tmux_boxstall() {
    srm ~/.config/tmux
    smd ~/.config/tmux
    smd ~/.tmux
    ln -s "$DOT_DIR/tmux/dot-config/tmux/tmux.conf" ~/.config/tmux/tmux.conf

    has_cmd tmux && return 0

    if has_cmd brew; then
        bi tmux
    else
        si tmux
        has_cmd tmux || (has_cmd pixi && pi tmux)
    fi

    has_cmd tmux || warn "tmux not installed! Qutting."
}

zsh_boxstall() {
    srm ~/.zshrc
    ln -s "$DOT_DIR/zsh/dot-zshrc" ~/.zshrc

    has_cmd zsh && return 0

    si zsh
    has_cmd zsh || (has_cmd pixi && pi zsh)

    has_cmd zsh || warn "zsh not installed! Qutting."
    set_zsh_as_default
}

terminal_boxstall() {
    terminal_bin_install
    jetbrains_nerd_font_install

    has_cmd ghostty && ghostty_config_install
    has_cmd kitty && kitty_config_install
    has_cmd alacritty && alacritty_config_install

    if has_cmd flatpak; then
        flathub_install
        ptyxis_install
        # ptyxis_config_install
    fi
}

dbox_nix_setup() {
    if ! has_cmd distrobox; then
        warn "distrobox not installed, skipping distrobox creation"
        return 1
    fi

    dbox_nix
}

slimbox_base() {
    cd "$HOME" || warn "could not cd to $HOME"

    "$1_packages"

    dotfiles_install
    bash_config_install
    starship_install

    dir_exists "$DOT_DIR" || err_exit "$DOT_DIR doesn't exist, Installation failed."
}

box_base() {
    slimbox_base "$1"

    zsh_boxstall
}

dbox_base() {
    is_distrobox || err_exit "This script is only for distrobox containers"
    [[ "$HOME" == *"boxes"* ]] || err_exit "This script is only for distrobox containers with isolated home directory"

    box_base "$1"
}

nixbox_base() {
    is_wsl && err_exit "This script is not for wsl. Use nixos-wsl instead."

    has_cmd curl || si curl

    nix_install
    # shellcheck disable=SC1091
    . /nix/var/nix/profiles/default/etc/profile.d/nix-daemon.sh

    if has_cmd git; then
        clone-update https://github.com/pervezfunctor/dotfiles.git "$DOT_DIR"
    else
        nix run --extra-experimental-features 'nix-command flakes' nixpkgs#git clone https://github.com/pervezfunctor/dotfiles.git "$DOT_DIR"
    fi

    nix run --extra-experimental-features 'nix-command flakes' nixpkgs#stow -- -d "$DOT_DIR" -t "$HOME" --dotfiles -R home-manager
    nix run --extra-experimental-features 'nix-command flakes' home-manager -- switch --flake "$DOT_DIR"/nixos-wsl/dot-config/home-manager\#nixos --impure -b bak
}

get_boxos() {
    if is_tw; then
        echo "tw"
    elif is_ubuntu; then
        echo "ubuntu"
    elif is_debian; then
        echo "debian"
    elif is_fedora; then
        echo "fedora"
    elif is_arch; then
        echo "arch"
    elif is_centos; then
        echo "centos"
    elif is_alpine; then
        echo "alpine"
    else
        echo "brew"
    fi
}

slimbox_groupstall() {
    slimbox_base "$(get_boxos)"
}

box_groupstall() {
    box_base "$(get_boxos)"
}

fullbox_groupstall() {
    box_base "$(get_boxos)"
    nvim_boxstall
    tmux_boxstall
    emacs_boxstall
}

dbox_groupstall() {
    dbox_base "$(get_boxos)"
}

nixbox_groupstall() {
    nixbox_base
}

tw-wslbox_groupstall() {
    is_tw || err_exit "This script is only for openSUSE Tumbleweed"

    sudo systemd-tmpfiles --create
    wslbox_base tw
}

nixos-wslbox_groupstall() {
    is_wsl || err_exit "This script is only for NixOS WSL"

    nix run --extra-experimental-features 'nix-command flakes' nixpkgs#git clone https://github.com/pervezfunctor/dotfiles.git "$DOT_DIR"
    nix run --extra-experimental-features 'nix-command flakes' nixpkgs#stow -- -d "$DOT_DIR" -t "$HOME" --dotfiles -R nixos-wsl nvim
    nix run --extra-experimental-features 'nix-command flakes' home-manager -- switch --impure -b bak
}

wslbox_groupstall() {
    if is_tw; then
        tw-wslbox_groupstall
    elif is_nixos; then
        nixos-wslbox_groupstall
    else
        box_base "$(get_boxos)"
    fi
}

wsl_groupstall() {
    wslbox_groupstall
    python_install
    brew_install
    bis stow carapace fzf trash-cli lazygit eza zoxide gh gum
    has_cmd delta || bi git-delta

    zsh_boxstall
}

centos-wsl_groupstall() {
    wsl_groupstall

    tmux_boxstall
    nvim_boxstall
}

centos-fast_groupstall() {
    is_centos || err_exit "This script is only for CentOS"

    sudo dnf update
    sudo dnf install -y git-core wget curl gcc make unzip tar tree neovim tmux \
        zsh gcc-c++ gdb clang lldb lld llvm bat gum ripgrep luarocks

    pixi_install
    pixi global install trash-cli fzf lazygit eza zoxide gh git-delta

    starship_install
    tmux_boxstall
    nvim_boxstall
    zsh_boxstall
}
