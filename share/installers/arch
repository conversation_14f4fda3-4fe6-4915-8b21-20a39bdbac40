#! /usr/bin/env bash

si() {
    sudo pacman -S --needed --quiet --noconfirm "$@"

    # for p in "$@"; do
    #     slog "Installing package $p"
    #     sudo pacman -S --needed --quiet --noconfirm "$p"
    # done
}

update_packages() {
    slog "Updating Arch"

    if ! sudo pacman -Syu --noconfirm --quiet; then
        err_exit "pacman update/upgrade failed, quitting"
    fi
}

arch_packages() {
    update_packages

    slog "Installing packages"

    si git-core github-cli git-delta unzip wget curl trash-cli tar stow \
        gcc make file starship gum wl-clipboard tree bat eza fzf ripgrep \
        zoxide fd htop sd yazi bat tealdeer lazygit libsecret

    slog "Installing packages done!"
}

core_install() {
    update_packages

    slog "Installing core packages"

    si curl wget git base-devel trash-cli tree micro tar unzip stow cmake zstd \
        fuse2 file libxcrypt-compat libsecret

    slog "Core packages installation done!"
}

sys_python_install() {
    slog "Installing python"

    si python python-pipx python-pip python-setuptools python-wheel python-virtualenv
    pipx install uv

    slog "Python installation done!"
}

yay_install() {
    has_cmd yay && return 1
    slog "Installing yay"

    frm /tmp/yay
    git clone https://aur.archlinux.org/yay.git /tmp/yay && cd /tmp/yay && makepkg --syncdeps --noconfirm --install && cd - && frm /tmp/yay

    slog "yay installation done!"
}

snap_install() {
    yay -Sy --noconfirm snapd
    sudo systemctl enable --now snapd
}

essential_install() {
    slog "Installing essential packages"

    sys_python_install

    si unarchiver zip gawk tmux pkg-config \
        p7zip cmake readline sqlite libffi zlib xz pkgfile gum
    sudo pkgfile --update

    slog "Essential packages installation done!"
}

cli_install() {
    slog "Installing cli tools using pacman"

    si neovim github-cli shellcheck shfmt python-pynvim zsh zsh-completions \
        luarocks duf lazygit starship eza ugrep git-delta navi \
        sd gdu bat hyperfine fd fzf ripgrep zoxide htop nushell bottom \
        plocate tealdeer television dysk yazi pixi procs dust direnv atuin \
        broot just glances curlie xh jq superfile choose lsd yq

    slog "cli tools installation done!"
}

cpp_install() {
    slog "Installing C++"

    si gcc gdb boost boost-libs catch2 libc++ clang llvm \
        doxygen graphviz ccache cppcheck pre-commit \
        valgrind ltrace strace lldb lld

    cmake_install
    conan_install

    slog "C++ installation done!"

    cmd_check gcc gdb make cmake conan clang clang++ clang-tidy clang-format
}

vscode_bin_install() {
    slog "Installing vscode"

    yay -Sy --noconfirm visual-studio-code-bin

    slog "vscode installation done!"

    cmd_check code
}

terminal_bin_install() {
    slog "Installing terminal"
    si ghostty
    slog "terminal installation done!"
}

ui_install() {
    slog "Installing ui"

    si chromium neovide gnome-keyring wl-clipboard flatpak

    flathub_install

    terminal_bin_install
    vscode_bin_install
    apps_install

    has_cmd virt-install && si virt-manager virt-viewer

    slog "ui installation done!"
}

cockpit_install() {
    has_cmd cockpit && return 1

    slog "Installing cockpit"

    si cockpit cockpit-machines cockpit-packagekit cockpit-pcp cockpit-podman cockpit-storaged
    sudo systemctl enable --now cockpit.socket

    slog "cockpit installation done!"
}

podman_install() {
    has_cmd podman && return 1

    slog "Installing podman"
    si podman podman-compose
    slog "podman installation done!"
}

vm_install() {
    slog "Installing vm"

    si libvirt qemu virt-install bridge-utils dnsmasq edk2-ovmf virglrenderer

    libvirt_config_install

    slog "vm installation done!"
}

docker_install() {
    has_cmd docker && return 1

    slog "Installing docker"

    si docker docker-compose
    docker_config_install

    slog "docker installation done!"
}

incus_install() {
    has_cmd incus && return 1

    slog "Installing incus"

    si incus
    incus_config_install

    slog "incus installation done!"
}

ct_install() {
    slog "Installing container tools"

    incus_install
    docker_install
    portainer_install
    podman_install
    cockpit_install

    si buildah distrobox

    slog "Container tools installation done!"
}

virt_install() {
    slog "Installing virtualization packages"

    ct_install
    vm_install

    code_server_install

    slog "Virtualization packages installation done!"
}
