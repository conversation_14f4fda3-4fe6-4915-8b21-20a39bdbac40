#! /usr/bin/env bash

webi_install() {
    has_cmd ~/.local/bin/webi && return 0

    curl -sS https://webi.sh/webi | sh
    webi pathman
    source_if_exists "$XDG_CONFIG_HOME/envman/PATH.env"

    cmd_check webi
}

mise_install() {
    if has_cmd ~/.local/bin/mise; then
        eval "$(~/.local/bin/mise activate bash)"
        return 0
    fi

    slog "Installing mise"
    curl https://mise.run | MISE_QUIET=1 sh
    eval "$(~/.local/bin/mise activate bash)"

    if has_cmd mise; then
        mise use -g usage
        mise settings experimental=true
        mise use -g cargo-binstall
    else
        warn "mise installation failed"
    fi
}

pixi_install() {
    has_cmd ~/.pixi/bin/pixi && return 0

    slog "Installing pixi"
    if has_cmd curl; then
        curl -fsSL https://pixi.sh/install.sh | bash
    elif has_cmd wget; then
        wget -qO- https://pixi.sh/install.sh | bash
    else
        warn "curl or wget not installed, skipping pixi installation"
        return 1
    fi

    slog "pixi installation done!"

    cmd_check ~/.pixi/bin/pixi
    cmd_check pixi
}

# pkgx_install() {
#     has_cmd pkgx && return 0

#     if has_cmd brew; then
#         brew install pkgx
#     else
#         curl https://pkgx.sh | sh
#     fi

#     cmd_check pkgx
# }

pkgx_install() {
    has_cmd ~/.local/bin/pkgx && return 0

    mkdir -p ~/.local/bin
    if has_cmd curl; then
        curl -fsSL "https://pkgx.sh/$(uname)/$(uname -m).tgz" | tar xz -C ~/.local/bin
    elif has_cmd wget; then
        wget -qO- "https://pkgx.sh/$(uname)/$(uname -m).tgz" | tar xz -C ~/.local/bin
    else
        warn "curl or wget not installed, skipping pkgx installation"
        return 1
    fi

    cmd_check pkgx
}

uv_install() {
    has_cmd uv && return 0

    if has_cmd pipx; then
        pipx install uv
    else
        curl -LsSf https://astral.sh/uv/install.sh | sh
    fi

    cmd_check uv
}

python_install() {
    slog "Installing python tools"

    has_cmd sys_python_install && sys_python_install

    if ! has_cmd python3; then
        warn "python3 not installed! Skipping python setup."
        return 1
    fi

    if ! has_cmd pipx; then
        if ! has_cmd pip; then
            python3 -m ensurepip --user --default-pip
            python3 -m pip install --user --force-reinstall --upgrade pip
        fi

        if has_cmd pip; then
            pip install --upgrade --force-reinstall pipx
            cmd_check pipx
        else
            warn "pip not installed! Skipping python setup."
        fi
    fi

    uv_install

    slog "Python tools installation done!"
}

flathub_install() {
    if ! has_cmd flatpak; then
        has_cmd si && si flatpak
    fi

    if has_cmd flatpak; then
        slog "Adding flathub remote"
        flatpak remote-add --if-not-exists flathub https://dl.flathub.org/repo/flathub.flatpakrepo --user
    else
        warn "flatpak not installed! Ignoring flathub config."
    fi
}

pnpm_install() {
    has_cmd pnpm && return 0

    slog "Installing pnpm"
    curl -fsSL https://get.pnpm.io/install.sh | sh -

    # docker container installation
    # wget -qO- https://get.pnpm.io/install.sh | ENV="$HOME/.bashrc" SHELL="$(which bash)" bash -
    slog "Installing node"
    pnpm env use --global latest

    slog "pnpm/node installation done!"

    cmd_check pnpm
}

kitty_bin_install() {
    if ! is_desktop; then
        warn "Not running desktop, skipping kitty installation"
        return 1
    fi

    has_cmd kitty && return 1
    has_cmd ~.local/kitty.app/bin/kitty && return 1

    curl -L https://sw.kovidgoyal.net/kitty/installer.sh | sh /dev/stdin

    # force link
    ln -sf ~/.local/kitty.app/bin/kitty ~/.local/kitty.app/bin/kitten ~/.local/bin/

    smd ~/.local/share/applications
    cp ~/.local/kitty.app/share/applications/kitty.desktop ~/.local/share/applications/
    cp ~/.local/kitty.app/share/applications/kitty-open.desktop ~/.local/share/applications/

    # Update the paths to the kitty and its icon in the kitty desktop file(s)
    sed -i "s|Icon=kitty|Icon=$(readlink -f ~)/.local/kitty.app/share/icons/hicolor/256x256/apps/kitty.png|g" ~/.local/share/applications/kitty*.desktop
    sed -i "s|Exec=kitty|Exec=$(readlink -f ~)/.local/kitty.app/bin/kitty|g" ~/.local/share/applications/kitty*.desktop

    echo 'kitty.desktop' >~/.config/xdg-terminals.list

    cmd_check kitty

    # @TODO: simpler
    # curl -L https://sw.kovidgoyal.net/kitty/installer.sh | sh /dev/stdin \
    # dest=~/.local/stow
    # cd ~/.local/stow
    # stow kitty.app
}

kitty_install() {
    kitty_bin_install
    kitty_config_install
}

ghostty_bin_install() {
    has_cmd ghostty && return 1

    local VERSION
    VERSION=$(curl -s https://api.github.com/repos/pkgforge-dev/ghostty-appimage/releases/latest | grep '"tag_name":' | sed -E 's/.*"([^"]+)".*/\1/')

    local ARCH
    ARCH=$(uname -m)

    wget "https://github.com/pkgforge-dev/ghostty-appimage/releases/download/${VERSION}/Ghostty-${VERSION}-${ARCH}.AppImage"
    chmod +x "Ghostty-${VERSION}-${ARCH}.AppImage"
    install "./Ghostty-${VERSION}-${ARCH}.AppImage" "$HOME/.local/bin/ghostty"
}

ghostty_install() {
    ghostty_bin_install
    ghostty_config_install
}

atomic_distrobox_install() {
    has_cmd distrobox && return 0

    slog "Installing distrobox"

    curl -s https://raw.githubusercontent.com/89luca89/distrobox/main/install | sh -s -- --prefix ~/.local

    slog "distrobox installation done!"
}

cmake_install() {
    has_cmd ~/.local/bin/cmake && return 1

    CMAKE_VERSION="4.0.1"
    ARCH=$(uname -m)
    CMAKE_BINARY_NAME="cmake-${CMAKE_VERSION}-linux-${ARCH}.sh"
    CMAKE_CHECKSUM_NAME="cmake-${CMAKE_VERSION}-SHA-256.txt"

    slog "Installing latest cmake"
    TMP_DIR=$(mktemp -d -t cmake-XXXXXXXXXX)
    if cd "${TMP_DIR}"; then
        curl -sSL "https://github.com/Kitware/CMake/releases/download/v${CMAKE_VERSION}/${CMAKE_BINARY_NAME}" -O
        curl -sSL "https://github.com/Kitware/CMake/releases/download/v${CMAKE_VERSION}/${CMAKE_CHECKSUM_NAME}" -O

        sha256sum -c --ignore-missing "${CMAKE_CHECKSUM_NAME}"
        local PREFIX
        PREFIX=~/.cmake

        sudo mkdir -p "${PREFIX}"
        sudo sh "${TMP_DIR}/${CMAKE_BINARY_NAME}" --prefix="${PREFIX}" --skip-license

        sudo ln -s "${PREFIX}/bin/cmake" ~/.local/bin/cmake
        sudo ln -s "${PREFIX}/bin/ctest" ~/.local/bin/ctest
        frm "${TMP_DIR}"
    fi

    slog "cmake installation done!"

    cmd_check cmake
}

generic-ct_groupstall() {
    has_cmd curl || has_cmd wget || err_exit "Install curl and run this script again."

    pixi_install
    pis git curl wget

    pkgx_install

    slog "Installing shell tools with pixi"
    pixi_shell_slim_install

    has_cmd git || fail "git not installed. Quitting."
    slog "Installing shell tools with pixi done!"

    dotfiles_install

    starship_install
    bash_config_install
}

generic_groupstall() {
    generic-ct_groupstall

    is_desktop && jetbrains_nerd_font_install
    is_desktop && kitty_bin_install

    if has_cmd podman || has_cmd docker; then
        atomic_distrobox_install
    else
        fail "No container runtime(docker or podman) installed, skipping distrobox installation"
    fi
}

gnome_extensions_install() {
    if ! has_cmd gext; then
        if ! has_cmd pipx; then
            warn "pipx not installed, skipping gnome extensions"
            return 1
        fi

        pipx install gnome-extensions-cli --system-site-packages
        cmd_check gext
    fi

    slog "Installing gnome extensions"

    <NAME_EMAIL>
    <NAME_EMAIL>
    <NAME_EMAIL>
    <NAME_EMAIL>
    <NAME_EMAIL>
    gext install just-perfection-desktop@just-perfection
    gext install blur-my-shell@aunetx
    <NAME_EMAIL>@gmail.com
    gext install AlphabeticalAppGrid@stuarthayhurst

    slog "gnome extensions installation done!"
}

gnome_settings_install() {
    if ! has_cmd gsettings; then
        warn "gsettings not found, skipping gnome basic settings"
        return 1
    fi

    slog "gnome settings"

    gsettings set org.gnome.desktop.input-sources xkb-options "['caps:ctrl_modifier']"
    gsettings set org.gnome.desktop.interface gtk-theme 'Adwaita-dark'
    gsettings set org.gnome.desktop.interface icon-theme 'Adwaita-dark'
    gsettings set org.gnome.desktop.interface cursor-theme 'Adwaita'
    gsettings set org.gnome.desktop.interface color-scheme 'prefer-dark'
    gsettings set org.gnome.desktop.interface gtk-key-theme "Emacs"
    gsettings set org.gnome.desktop.interface accent-color 'purple'

    # Use 4 fixed workspaces instead of dynamic mode
    gsettings set org.gnome.mutter dynamic-workspaces false
    gsettings set org.gnome.desktop.wm.preferences num-workspaces 4

    # Center new windows in the middle of the screen
    gsettings set org.gnome.mutter center-new-windows true

    # Set JetBrains Mono as the default monospace font
    gsettings set org.gnome.desktop.interface monospace-font-name 'JetbrainsMono Nerd Font 11'

    slog "gnome settings done!"
}

gnome_flatpaks_install() {
    fpi page.tesk.Refine
    fpi com.github.tchx84.Flatseal
    fpi io.github.flattool.Ignition
    fpi io.github.flattool.Warehouse
    fpi io.missioncenter.MissionCenter
    fpi org.gnome.World.PikaBackup
    fpi org.gnome.Papers
}

# https://github.com/basecamp/omakub/blob/master/install/desktop/set-gnome-extensions.sh
gnome_config_install() {
    if ! is_gnome; then
        warn "Not running GNOME, skipping GNOME config"
        return 1
    fi

    slog "gnome config"

    if is_ubuntu; then
        has_cmd si && si gnome-shell-extension-manager gnome-tweak-tool gnome-sushi gnome-software-plugin-flatpak
    elif is_fedora && ! is_atomic; then
        has_cmd si && si gnome-extensions-app gnome-tweaks
    fi

    if is_ubuntu; then
        gnome-<NAME_EMAIL>
        gnome-<NAME_EMAIL>
        gnome-<NAME_EMAIL>
        gnome-<NAME_EMAIL>
    fi

    gnome_settings_install
    #   gnome_keybindings_install
    gnome_flatpaks_install
    gnome_extensions_install

    slog "gnome config done!"
}

fedora-atomic-tbox_groupstall() {
    is_std_atomic || err_exit "This script is only for Atomic Host"

    slog "Fedora Atomic Host setup"

    jetbrains_nerd_font_install

    slog "Installing apps"
    apps_slim_install

    slog "Setting up default distrobox for development"

    # shellcheck disable=SC2119
    tbox_default

    slog "Fedora Atomic Host With default toolbox setup done!"
}

ublue_groupstall() {
    is_bluefin || is_aurora || err_exit "This script is only for Bluefin/Aurora"

    slog "ublue setup..."

    dotfiles_install
    fonts_install

    brew_shell_slim_install
    apps_slim_install

    vscode-extensions_install
    vscode_config_install
    git_config_install
    bash_config_install
    tmux_config_install
    nvim_config_install

    slog "ublue setup done!"
}

fedora-atomic_groupstall() {
    is_std_atomic || err_exit "This script is only for Atomic Host"

    slog "Fedora Atomic Host setup"

    generic-ct_groupstall
    jetbrains_nerd_font_install
    atomic_distrobox_install

    slog "Installing apps"
    apps_slim_install
    fpi org.gnu.emacs

    is_gnome && gnome_config_install
    is_sway && sway_config_install

    slog "Setting up default distrobox for development"

    # shellcheck disable=SC2119
    dbox_with_home

    stowdf emacs-nano

    slog "Fedora Atomic Host setup done!"
}

fedora-layered_groupstall() {
    is_std_atomic || err_exit "This script is only for Atomic Host"

    slog "Fedora Atomic Host setup"

    pixi_install
    pkgx_install

    pis wget
    pixi_shell_slim_install

    dotfiles_install
    bash_config_install
    zsh_boxstall

    fonts_install
    atomic_distrobox_install
    vscode_config_install
    # docker_config_install

    apps_slim_install

    is_gnome && gnome_config_install
    is_sway && sway_config_install

    slog "Fedora layered setup done! Reboot your system."
}

vscode_flatpak_install() {
    fpi com.visualstudio.code
    flatpak install com.visualstudio.code.tool.podman
    alias code="flatpak run com.visualstudio.code"
    xargs -L 1 flatpak run com.visualstudio.code --install-extension <"$DOT_DIR/extras/vscode/extensions/default"
    vscode-flatpak_config_install
}

ptyxis_install() {
    if ! has_cmd flatpak; then
        warn "flatpak not installed, skipping ptyxis installation."
        return 1
    fi

    if ! flatpak list | grep -q -i Ptyxis >/dev/null; then
        slog "Installing Ptyxis"
        fpi app.devsuite.Ptyxis
    fi

    # has_cmd gsettings || return 1

    # slog "Configuring Ptyxis"

    # gsettings set org.gnome.Ptyxis use-system-font false
    # gsettings set org.gnome.Ptyxis font-name 'JetBrains Mono Medium 12'
    # gsettings set org.gnome.Ptyxis interface-style 'dark'

    # local profid
    # profid=$(gsettings get org.gnome.Ptyxis default-profile-uuid)
    # gsettings set "org.gnome.Ptyxis.Profile:/${profid}/" palette 'Catppuccin Mocha'

    slog "Ptyxis installation and configuration done!"
}

apps_slim_install() {
    if ! has_cmd flatpak; then
        warn "flatpak not installed, skipping flatpak apps"
        return 1
    fi

    flathub_install

    ptyxis_install

    has_cmd distrobox && fpi io.github.dvlv.boxbuddyrs
    fpi org.gnome.Boxes
    fpi us.zoom.Zoom # unverified
}

apps_install() {
    slog "Installing flatpak apps"

    apps_slim_install

    fpi md.obsidian.Obsidian
    has_cmd podman && fpi io.podman_desktop.PodmanDesktop
    fpi org.telegram.desktop
    fpi io.github.getnf.embellish
    fpi com.bitwarden.desktop
    fpi sh.cider.Cider
    # fpi io.github.shiftey.Desktop # unverified
    # fpi org.qbittorrent.qBittorrent # verified
    # fpi org.deluge_torrent.deluge # unverified

    # fpi org.wezfurlong.wezterm
    # alias wezterm="flatpak run org.wezfurlong.wezterm"

    slog "Flatpak apps installation done!"
}

more_apps_install() {
    fpi org.wireshark.Wireshark

    # fpi io.dbeaver.DBeaverCommunity
    # fpi dev.neovide.neovide
    # fpi io.github.zyedidia.micro
    # fpi com.jetbrains.CLion
    # fpi com.google.Chrome
}

incus_config_install() {
    slog "incus config"

    if ! has_cmd incus; then
        warn "incus not installed, skipping incus config"
        return 0
    fi
    slog "incus config"

    sudo usermod -aG incus "$USER"
    sudo usermod -aG incus-admin "$USER"
    sudo systemctl enable --now incus.socket
    sudo systemctl enable --now incus.service

    if has_cmd firewalld; then
        sudo firewall-cmd --zone=trusted --change-interface=incusbr0 --permanent
        sudo firewall-cmd --reload
    fi

    newgrp incus-admin <<EOF
    sudo incus admin init --minimal
EOF

    slog "incus config done!"
}

atomic_nvim_install() {
    is_mac && return 0

    has_cmd nvim && return 0

    slog "Installing neovim..."

    curl -LO https://github.com/neovim/neovim/releases/latest/download/nvim.appimage
    chmod u+x nvim.appimage

    ./nvim.appimage --appimage-extract

    sudo mv squashfs-root /
    sudo ln -s /squashfs-root/AppRun /usr/bin/nvim
    frm nvim.appimage

    cmd_check nvim
}

micro_install() {
    has_cmd micro && return 0

    slog "Installing micro"

    curl https://getmic.ro | bash
    mv micro "$HOME/.local/bin/"

    slog "micro installation done!"

    cmd_check micro
}

rust_install() {
    source_if_exists "$HOME/.cargo/env"

    has_cmd rustup && return 0

    slog "Installing rust"
    curl --proto '=https' --tlsv1.2 -sSf https://sh.rustup.rs | sh -s -- -y
    source_if_exists "$HOME/.cargo/env"

    slog "rust installation done!"

    cmd_check rustc
}

docker_config_install() {
    if ! has_cmd docker; then
        warn "docker not installed, skipping docker post install configuration"
        return 1
    fi

    if has_cmd brew; then
        bi jesseduffield/lazydocker/lazydocker
        bi lazydocker
    fi

    if ! grep -q docker /etc/group 2>/dev/null; then
        sudo groupadd docker
    fi

    if ! groups "$USER" | grep -q docker 2>/dev/null; then
        sudo usermod -aG docker "$USER"
    fi

    sudo systemctl --now enable docker
    sudo systemctl --now enable containerd
}

conan_install() {
    has_cmd conan && return 1

    slog "Installing conan"
    if has_cmd pipx; then
        pipx install conan
    else
        uv tool install conan
    fi

    slog "conan installation done!"

    cmd_check conan
}

monaspace_install() {
    file_exists ~/.local/share/fonts/MonaspaceRadon-Regular.otf && return 0

    smd ~/.local/share/fonts

    frm /tmp/monaspace /tmp/monaspace.zip
    wget -nv https://github.com/githubnext/monaspace/releases/download/v1.000/monaspace-v1.000.zip -O /tmp/monaspace.zip
    unzip -qq -d /tmp/monaspace -o /tmp/monaspace.zip

    cp /tmp/monaspace/monaspace-*/fonts/otf/* ~/.local/share/fonts
    cp /tmp/monaspace/monaspace-*/fonts/variable/* ~/.local/share/fonts

    frm /tmp/monaspace /tmp/monaspace.zip
}

cascadia_nerd_font_install() {
    file_exists ~/.local/share/fonts/CaskaydiaMonoNerdFont-Regular.ttf && return 0

    smd ~/.local/share/fonts
    frm /tmp/cascadia /tmp/CascadiaMono.zip
    wget -nv https://github.com/ryanoasis/nerd-fonts/releases/download/v3.4.0/CascadiaMono.zip -O /tmp/CascadiaMono.zip
    unzip -qq -d /tmp/cascadia -o /tmp/CascadiaMono.zip
    cp /tmp/cascadia/*.ttf ~/.local/share/fonts
    frm /tmp/cascadia /tmp/CascadiaMono.zip
}

monaspace_nerd_font_install() {
    file_exists ~/.local/share/fonts/MonaspiceRnNerdFont-Regular.otf && return 0

    smd ~/.local/share/fonts
    frm /tmp/monaspace /tmp/Monaspace.zip
    wget -nv https://github.com/ryanoasis/nerd-fonts/releases/download/v3.4.0/Monaspace.zip -O /tmp/Monaspace.zip
    unzip -qq -d /tmp/monaspace -o /tmp/Monaspace.zip
    cp /tmp/monaspace/*.otf ~/.local/share/fonts
    frm /tmp/monaspace /tmp/Monaspace.zip
}

jetbrains_nerd_font_install() {
    file_exists ~/.local/share/fonts/JetBrainsMonoNLNerdFontPropo-Regular.ttf && return 0

    slog "Installing JetBrains Mono Nerd Font"
    smd ~/.local/share/fonts
    frm /tmp/jetbrains-mono/tmp/jetbrains-mono.zip
    wget -nv https://github.com/ryanoasis/nerd-fonts/releases/download/v3.4.0/JetBrainsMono.zip -O /tmp/jetbrains-mono.zip
    unzip -qq -d /tmp/jetbrains-mono -o /tmp/jetbrains-mono.zip
    cp /tmp/jetbrains-mono/*.ttf ~/.local/share/fonts
    frm /tmp/jetbrains-mono/tmp/jetbrains-mono.zip
    slog "JetBrains Mono Nerd Font installation done!"
}

maple_font_install() {
    file_exists ~/.local/share/fonts/MapleMono-NF-Regular.ttf && return 0

    slog "Installing Maple Mono Font"
    smd ~/.local/share/fonts
    frm /tmp/maple-mono/tmp/maple-mono.zip
    wget -nv https://github.com/subframe7536/maple-font/releases/download/v7.2/MapleMono-NF.zip -O /tmp/maple-mono.zip
    unzip -qq -d /tmp/maple-mono -o /tmp/maple-mono.zip
    cp /tmp/maple-mono/*.ttf ~/.local/share/fonts
    frm /tmp/maple-mono/tmp/maple-mono.zip
    slog "Maple Mono Font installation done!"
}

nerd_font_install() {
    smd ~/.local/share/fonts

    cascadia_nerd_font_install
    jetbrains_nerd_font_install
    monaspace_nerd_font_install
}

fonts_install() {
    if ! has_cmd wget || ! has_cmd unzip; then
        warn "wget and unzip not installed, skipping fonts"
        return 1
    fi

    slog "Installing fonts"

    monaspace_install
    nerd_font_install
    maple_font_install

    # if is_apt; then
    #     si fonts-cascadia-code fonts-jetbrains-mono
    # elif is_rh; then
    #     is_fedora || si cascadia-fonts-all jetbrains-mono-fonts-all
    # fi

    slog "Fonts installation done!"
}

starship_install() {
    has_cmd starship && return 0

    smd ~/.local/bin
    curl -sS https://starship.rs/install.sh | sh -s -- -y -b ~/.local/bin
}

vscode-flatpak_config_install() {
    slog "vscode config"

    smd ~/.var/app/com.visualstudio.code/config/Code/User
    safe-cp "$DOT_DIR/extras/vscode/flatpak-settings.json" ~/.var/app/com.visualstudio.code/config/Code/User/settings.json

    slog "vscode config done!"
}

sway-waybar_config_install() {
    slog "waybar config"
    has_cmd waybar || return 1
    stowgf waybar
    slog "waybar config done!"
}

wlogout_config_install() {
    slog "wlogout config"
    has_cmd wlogout || return 1
    stowgf wlogout
    slog "wlogout config done!"
}

sway_config_install() {
    has_cmd sway || return 1
    slog "sway config"
    stowgf sway
    slog "sway config done!"
}

rofi_config_install() {
    has_cmd rofi || return 1

    slog "rofi config"
    stowgf rofi
    slog "rofi config done!"
}

dbox-docker-dev_groupstall() {
    is_ubuntu || err_exit "This script is only for Ubuntu"
    has_cmd docker || err_exit "This script requires docker"

    docker_config_install

    min_groupstall
    si firefox gnome-keyring wl-clipboard libsecret
    sudo apt-get purge fzf

    starship_install
    vscode_install

    has_cmd zsh && zsh_config_install
}

github_user_setup() {
    if ! has_cmd gh; then
        warn "gh not installed, skipping github user setup"
        return 1
    fi

    if has_cmd gh; then
        slog "Authenticating github(gh)"
        gh auth login
    fi
}

box-default_groupstall() {
    shell_groupstall
    vscode_install
    fonts_install

    si firefox gnome-keyring wl-clipboard kitty libsecret
    gnome-keyring-daemon -s -d --components=pkcs11,secrets,ssh
    systemctl --user enable --now dbus
}

default_dbox_exports() {
    distrobox-export --bin "$(which stow)"
    distrobox-export --bin "$(which wl-copy)"
    distrobox-export --bin "$(which wl-paste)"
    distrobox-export --bin "$(which code)"
    distrobox-export --app "Visual Studio Code"
    distrobox-export --app "kitty"
}

box-default-atomic_groupstall() {
    box-default_groupstall
    default_dbox_exports
}

# shellcheck disable=SC2120
tbox_group() {
    has_cmd toolbox || return 0

    toolbox --assumeyes create

    local group=${1:-shell}
    toolbox run bash -c "$(curl -sSL https://dub.sh/aPKPT8V)" -- "$group"
}

tbox_default() {
    tbox_group box-default-atomic
}

dbox_default() {
    has_cmd distrobox || return

    slog "Creating default distrobox"
    distrobox create --yes
    # distrobox enter -- env -u PATH bash -ic "$(curl -sSL https://dub.sh/aPKPT8V)" -- box-default-atomic
    distrobox enter -nw --clean-path -- bash -c "$(curl -sSL https://dub.sh/aPKPT8V)" -- box-default-atomic
    slog "Default distrobox created!"
}

# shellcheck disable=SC2120
dbox_with_home() {
    if ! has_cmd distrobox; then
        fail "distrobox not installed, skipping distrobox creation"
        return 1
    fi

    slog "Creating default distrobox"

    local os
    os=${1:-fedora-init}
    local osname
    osname=${2:-ilm}
    distrobox list | grep -q "^${osname}$" && return

    "dbox-$os" "$osname"
    distrobox enter -nw --clean-path --name "${osname}" -- bash -c "$(curl -sSL https://dub.sh/aPKPT8V)" -- box-default-atomic
    slog "distrobox ${osname} created!"
}

sway-full_config_install() {
    sway-waybar_config_install
    foot_config_install
    kitty_config_install
    sway_config_install
    rofi_config_install
    wlogout_config_install
}

hypr-waybar_config_install() {
    has_cmd waybar || return 1

    slog "waybar config"
    stowgf hypr-waybar
    slog "waybar config done!"
}

hyprland_config_install() {
    slog "hypr config install"

    hypr-waybar_config_install
    wlogout_config_install
    rofi_config_install
    kitty_config_install

    slog "hypr config done!"
}
