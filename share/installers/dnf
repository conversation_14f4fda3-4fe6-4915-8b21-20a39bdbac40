#! /usr/bin/env bash

si() {
    if has_cmd dnf5; then
        # fedora 41
        sudo dnf5 -q -y \
            --setopt=install_weak_deps=False \
            --setopt=fastestmirror=True \
            --setopt=keepcache=False \
            --setopt=defaultyes=True \
            --setopt=max_parallel_downloads=10 \
            --setopt=metadata_timer_sync=0 \
            --setopt=keepcache=False \
            --setopt=defaultyes=True \
            install --skip-unavailable --skip-broken "$@"
    else
        # needed to support centos and rocky
        for p in "$@"; do
            slog "Installing package $p"
            sudo dnf -q -y install "$p"
        done
    fi
}

enable_epel() {
    slog "Enabling EPEL"
    if is_rocky || is_centos; then
        sudo dnf config-manager --set-enabled crb
    fi

    is_fedora && sudo dnf-3 config-manager --set-enabled crb
    si epel-release
}

docker_fedora_repo() {
    has_cmd docker && return 0

    si dnf-plugins-core

    is_rocky && sudo dnf config-manager --add-repo https://download.docker.com/linux/centos/docker-ce.repo
    is_fedora && sudo dnf-3 config-manager --add-repo https://download.docker.com/linux/fedora/docker-ce.repo
}

vscode_fedora_repo() {
    has_cmd code && return 1

    [ -f /etc/yum.repos.d/vscode.repo ] && return 1

    slog "Adding vscode repo"

    sudo rpm --import https://packages.microsoft.com/keys/microsoft.asc
    sudo sh -c 'echo -e "[code]\nname=Visual Studio Code\nbaseurl=https://packages.microsoft.com/yumrepos/vscode\nenabled=1\ngpgcheck=1\ngpgkey=https://packages.microsoft.com/keys/microsoft.asc" > /etc/yum.repos.d/vscode.repo'

    slog "vscode repo added!"
}

update_packages() {
    slog "Updating fedora"
    if ! { sudo dnf update -q -y && sudo dnf upgrade -q -y; }; then
        err_exit "dnf update/upgrade failed, quitting"
    fi
}

fedora_packages() {
    update_packages

    slog "Installing packages"

    si git-core gh git-delta unzip wget curl trash-cli tar stow gcc make file \
        gum wl-clipboard tree fzf ripgrep zoxide fd htop bat tealdeer plocate \
        cheat libsecret

    slog "Installing packages done!"
}

rpm-ostree_packages() {
    slog "Installing packages"

    vscode_fedora_repo
    # docker_fedora_repo

    local PKGS=()

    has_cmd stow || PKGS+=("stow")
    has_cmd zsh || PKGS+=("zsh")
    has_cmd gcc || PKGS+=("gcc")
    has_cmd make || PKGS+=("make")
    has_cmd wl-copy || PKGS+=("wl-clipboard")
    has_cmd virsh || PKGS+=("libvirt")
    has_cmd virt-install || PKGS+=("virt-install")
    has_cmd code || PKGS+=("code")
    # has_cmd docker || PKGS+=("docker-ce" "docker-ce-cli" "containerd.io" "docker-buildx-plugin" "docker-compose-plugin")

    if [[ ${#PKGS[@]} -eq 0 ]]; then
        slog "No packages to install"
        return 0
    fi

    if has_cmd stow || has_cmd zsh || has_cmd gcc || has_cmd make || has_cmd wl || has_cmd virsh || has_cmd virt || has_cmd code; then
        warn "This script is supposed to be run only once"
    fi

    sudo rpm-ostree install -q -y --apply-live "${PKGS[@]}"

    sudo systemctl enable --now libvirtd

    warn "Installing packages done! Note that you need to reboot for the changes to take effect."
}

centos_packages() {
    update_packages

    slog "Installing packages"

    si git unzip wget curl tar gcc make gum tree bat ripgrep \
        htop bat plocate file just emacs-nox neovim tmux zsh libsecret

    slog "Installing packages done!"
}

base_dev_install() {
    if dnf group list -q | grep -q "c-development"; then
        si @c-development
    elif dnf group list -q | grep -q "Development Tools"; then
        sudo dnf group install --setop=install_weak_deps=False --with-optional -y "Development Tools"
    else
        si gcc make cmake autoconf automake binutils expect flex bison glibc-devel
    fi
}

sys_python_install() {
    si python3 python3-virtualenv pipx python3-pip python3-setuptools python3-wheel
    pipx install uv
    pipx ensurepath
}

core_install() {
    slog "Installing core packages"

    si 'dnf-command(copr)'
    si 'dnf-command(config-manager)'
    is_fedora || enable_epel
    is_fedora && si dnf5-plugins

    update_packages
    base_dev_install

    si curl wget git-core trash-cli cmake tree tar unzip util-linux-user which \
        fuse fuse-libs zstd file libsecret

    is_fedora && si stow

    slog "Core packages installation done!"
}

essential_install() {
    slog "Installing essential packages"

    sys_python_install

    si zip p7zip cmake unar gawk readline-devel sqlite-devel libffi-devel \
        bzip2-devel xz-devel gum

    slog "Essential packages installation done!"
}

cli_install() {
    slog "Installing cli tools using dnf"

    local base_pkgs=(zsh neovim ShellCheck tmux pkg-config urlview htop gh fzf ripgrep
        fd-find bat plocate luarocks tealdeer zsh lsd)

    local fedora_pkgs=()
    if is_fedora; then
        fedora_pkgs=(python3-neovim zoxide hyperfine git-delta procscheat navi
            ugrep micro direnv yq)
    fi

    si "${base_pkgs[@]}" "${fedora_pkgs[@]}"

    slog "cli tools installation done!"
}

snap_install() {
    has_cmd snap && return 1

    slog "Installing snapd"
    si snapd
    sudo systemctl enable --now snapd.socket
    sudo systemctl enable --now snapd
    [ -e /snap ] || sudo ln -s /var/lib/snapd/snap /snap

    slog "snapd setup done!"
}

cpp_install() {
    slog "Installing C++"

    si gcc gcc-c++ gdb valgrind systemtap ltrace strace clang clang-devel \
        clang-tools-extra clang-libs clang-analyzer lldb lld llvm llvm-devel \
        graphviz ccache cppcheck pre-commit cmake

    conan_install
    cmake_install

    if is_rocky; then
        si boost1.78 boost1.78-devel boost1.78-static catch-devel
    elif is_fedora; then
        si boost boost-devel boost-static catch-devel
        sudo dnf groupinstall -q -y --with-optional "C Development Tools and Libraries"
    fi

    slog "C++ installation done!"
}

vscode_bin_install() {
    has_cmd code && return 1

    slog "Installing vscode"

    vscode_fedora_repo

    dnf check-update
    si code

    slog "vscode installation done!"

    cmd_check code
}

terminal_bin_install() {
    has_cmd ghostty && return 1
    has_cmd kitty && return 1
    has_cmd alacritty && return 1

    if is_fedora; then
        slog "Installing ghostty"
        sudo dnf copr -y enable pgdev/ghostty
        si ghostty

        cmd_check ghostty
        slog "ghostty installation done!"
    else
        slog "Installing kitty"
        si kitty
        slog "kitty installation done!"

        if ! has_cmd kitty; then
            slog "Installing alacritty"
            si alacritty
            cmd_check alacritty
            slog "alacritty installation done!"
        fi
    fi
}

ui_install() {
    slog "Installing ui"

    si flatpak chromium gnome-keyring wl-clipboard flathub_install

    has_cmd virt-install && si virt-manager virt-viewer

    terminal_bin_install

    vscode_bin_install
    apps_install

    slog "ui installation done!"
}

cockpit_install() {
    has_cmd cockpit && return 1

    slog "Installing cockpit" si cockpit cockpit-machines cockpit-podman \
        cockpit-storaged cockpit-bridge cockpit-networkmanager cockpit-selinux \
        cockpit-system firewalld cockpit-packagekit

    sudo systemctl enable --now cockpit.socket
    sudo systemctl enable --now firewalld
    sudo firewall-cmd --add-service=cockpit
    sudo firewall-cmd --add-service=cockpit --permanent

    slog "cockpit installation done!"
}

docker_install() {
    has_cmd docker && return 1

    slog "Installing docker"

    docker_fedora_repo

    si docker-ce docker-ce-cli containerd.io docker-buildx-plugin \
        docker-compose-plugin

    docker_config_install

    slog "docker installation done!"
}

podman_install() {
    slog "Installing podman"
    si podman podman-compose podman-tui toolbox
    slog "podman installation done!"
}

incus_install() {
    if is_centos; then
        warn "incus not available for CentOS, skipping"
        return 1
    fi

    slog "Installing incus"

    if ! is_fedora; then
        sudo dnf -y copr enable neil/incus
    fi

    si incus
    incus_config_install

    slog "incus installation done!"
}

pm_install() {
    si lm_sensors stress-ng taliscale wireguard-tools smartmontools memtest86+ \
        glmark2
}

vm_install() {
    slog "Installing libvirt"

    if dnf group list -q | grep -q "System Tools"; then
        sudo dnf groupinstall -q -y --with-optional "System Tools"
    elif dnf group list -q | grep -q "system-tools"; then
        si @system-tools
    fi

    si libvirt qemu qemu-kvm virt-install bridge-utils virglrenderer

    libvirt_config_install

    slog "libvirt installation done!"
}

ct_install() {
    slog "Installing container tools"

    incus_install
    docker_install
    portainer_install
    podman_install
    cockpit_install

    si buildah distrobox

    slog "Container tools installation done!"
}

virt_install() {
    slog "Installing virtualization packages"

    ct_install
    vm_install

    code_server_install

    slog "Virtualization packages installation done!"
}

more_virt_install() {
    si samba-dcerpc samba-ldb-ldap-modules samba-winbind-clients
    si samba-winbind-modules samba
    si cockpit-bridge cockpit-selinux cockpit-storaged cockpit-system
    si cockpit-machines cockpit-networkmanager cockpit-system cockpit-storaged
    si cockpit-ostree cockpit-podman podman-tui podmansh powertop
    si qemu-device-display-virtio-gpu qemu-device-display-virtio-vga qemu-char-spice
    si qemu-device-usb-redirect qemu-img qemu-system-x86-core qemu-user-binfmt
    si qemu-user-static qemu udica # rocm-hip rocm-opencl
    si virt-viewer libvirt libvirt-nss podman-bootc

    si virt-manager containerd.io dbus-x11 docker-ce docker-ce-cli docker-buildx-plugin docker-compose-plugin

    # si svirt incus incus-agent lxc lxd-agent lxd kcli ubuntu-family-fonts
}

more_ui_install() {
    si wireguard-tools xprop solaar stress-ng usbmuxd
    si mesa-libGLU playerctl pulseaudio-utils

    if is_kde; then
        si libadwaita-qt5 libadwaita-qt6 kde-runtime-docs kdeplasma-addons
        si plasma-wallpapers-dynamic
    elif is_gnome; then
        si gnome-shell-extension-appindicator
        si gnome-shell-extension-caffeine gnome-shell-extension-dash-to-dock
        si gnome-shell-extension-gsconnect gnome-shell-extension-blur-my-shell
        # si gnome-shell-extension-search-light gnome-shell-extension-logo-menu
        si libgda libgda-sqlite
        si libratbag-ratbagd nautilus-gsconnect openssh-askpass yaru-theme
    fi

}

more_fonts_install() {
    si cascadia-code-fonts adobe-source-code-pro-fonts mozilla-fira-mono-fonts
    si jetbrains-mono-fonts-all google-go-mono-fonts ibm-plex-mono-fonts
    si google-droid-sans-mono-fonts powerline-fonts fira-code-fonts
}

more_essential_install() {
    bi glow gum lm_sensors stress-ng setools-console sysprof
    si rclone restic wl-clipboard samba tailscale wireguard-tools
    si git-credential-libsecret jetbrains-mono-fonts-all
    si edk2-ovmf genisoimage iotop p7zip-plugins p7zip
    si bash-color-prompt bcache-tools evtest fastfetch firewall-config
    si hplip ifuse input-remapper libimobiledevice libxcrypt-compat
}

tiling_common_install() {
    slog "Installing tiling common packages"

    si waybar pavucontrol Thunar wl-clipboard thunar-archive-plugin
    si network-manager-applet blueman gnome-keyring-pam gnome-keyring
    si gnome-themes-extra kanshi sddm playerctl wlsunset rofi-wayland
    si rofi-themes dunst swaync lxqt-policykit network-manager-applet
    si xdg-desktop-portal-gtk pmv imv mpv wlogout qt6-qtquickcontrols2 qt6-qtsvg
    si SwayNotificationCenter

    slog "Tiling common packages installation done!"
}

hyprland_bin_install() {
    tiling_common_install
    slog "Installing hyprland"

    si hyprland hyprland-devel hyprutils hyprutils-devel
    si hypridle hyprpaper hyprpicker hyprlock kitty hyprcursor

    slog "hyprland installation done!"
}

sway_bin_install() {
    tiling_common_install

    slog "Installing sway"

    si sway swaybg swayidle sway-systemd swaylock sddm-wayland-sway
    si sway-config-fedora kitty

    slog "sway installation done!"
}
