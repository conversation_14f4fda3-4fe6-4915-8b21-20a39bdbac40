#! /usr/bin/env bash

export XDG_CONFIG_HOME=${XDG_CONFIG_HOME:-$HOME/.config}
export DOT_DIR=${DOT_DIR:-$HOME/.ilm}

[[ -d "$DOT_DIR" ]] || return

# shellcheck disable=SC1091
source "$DOT_DIR/share/shellrc"

[[ $- == *i* ]] || return

export CARAPACE_BRIDGES='zsh,fish,bash,inshellisense' # optional
# shellcheck disable=SC1090
has_cmd carapace && source <(carapace _carapace)

has_cmd ~/.local/bin/mise && eval "$(~/.local/bin/mise activate bash)"
has_cmd ~/.pixi/bin/pixi && eval "$(~/.pixi/bin/pixi completion --shell bash)"

source_if_exists "$DOT_DIR/share/fns"
source_if_exists "$DOT_DIR/share/aliases"

source_if_exists "$HOME/.localbashrc"
source_if_exists "$XDG_CONFIG_HOME/localbashrc"

is_ublue && return

if has_cmd starship; then
    eval "$(starship init bash)"
    has_cmd zoxide && eval "$(zoxide init bash)"
    # shellcheck disable=SC1090
    has_cmd fzf && source <(fzf --bash)
    # has_cmd direnv && eval "$(direnv hook bash)"
    # has_cmd atuin && eval "$(atuin init bash --disable-up-arrow --disable-ctrl-r)"
fi

# one_shell_tmux
