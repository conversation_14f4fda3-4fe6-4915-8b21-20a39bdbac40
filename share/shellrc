# shellcheck disable=SC2148
export DOT_DIR=${DOT_DIR:-$HOME/.ilm}

# shellcheck disable=SC1091
source "$DOT_DIR/share/utils"

### "nvim" as manpager
has_cmd nvim && export MANPAGER="nvim +Man!"

export LANG=en_US.UTF-8

export HOMEBREW_NO_BOTTLE_SOURCE_FALLBACK=1

export ELECTRON_OZONE_PLATFORM_HINT=auto

source_if_exists "$HOME/.config/envman/load.sh"

source_if_exists "$HOME/.cargo/env"

source_if_exists /nix/var/nix/profiles/default/etc/profile.d/nix-daemon.sh

has_cmd webi && source_if_exists "$XDG_CONFIG_HOME/envman/PATH.env"

export DOTNET_ROOT="$HOME/.dotnet"
export GOPATH="$HOME/go"

if has_cmd /opt/homebrew/bin/brew; then
    eval "$(/opt/homebrew/bin/brew shellenv)"
elif [[ -d /home/<USER>/.linuxbrew ]]; then
    eval "$(/home/<USER>/.linuxbrew/bin/brew shellenv)"
fi

spath_export "/usr/bin"
spath_export "/snap/bin"
spath_export "$GOPATH/bin"
spath_export "$XDG_CONFIG_HOME/emacs/bin"
spath_export "$HOME/.local/bin"
spath_export "$HOME/bin"
spath_export "$HOME/.bin"
spath_export "$HOME/.ilm/bin"
spath_export "$HOME/Applications"
spath_export "$HOME/.local/share/pypoetry"
spath_export "$XDG_CONFIG_HOME/Code/User/globalStorage/ms-vscode-remote.remote-containers/cli-bin"
spath_export "$HOME/.console-ninja/.bin"
spath_export "$HOME/.pixi/bin"

if [ -z "$XDG_DATA_HOME" ]; then
    export XDG_DATA_HOME="$HOME/.local/share"
fi

if [ -z "$XDG_CACHE_HOME" ]; then
    export XDG_CACHE_HOME="$HOME/.cache"
fi

if [[ -d "$HOME/.pyenv" ]]; then
    export PYENV_ROOT="$HOME/.pyenv"
    spath_export "$PYENV_ROOT/bin"
    eval "$(pyenv init -)"
elif has_cmd "$HOME/miniconda3/bin/conda"; then
    if __conda_setup="$("$HOME/miniconda3/bin/conda" 'shell.zsh' 'hook' 2>/dev/null)"; then
        eval "$__conda_setup"
    else
        if [[ -f "/$HOME/miniconda3/etc/profile.d/conda.sh" ]]; then
            source_if_exists "/$HOME/miniconda3/etc/profile.d/conda.sh"
        else
            export PATH="/$HOME/miniconda3/bin:$PATH"
        fi
    fi
    unset __conda_setup
fi

if [[ -d "$HOME/.local/share/pnpm" ]]; then
    # pnpm
    export PNPM_HOME="$HOME/.local/share/pnpm"
    export PATH="$PNPM_HOME:$PATH"
    # pnpm end

    if [[ -f "$XDG_CONFIG_HOME/tabtab/zsh/__tabtab.zsh" ]]; then
        source_if_exists "$XDG_CONFIG_HOME/tabtab/zsh/__tabtab.zsh"
    fi

fi

vterm_printf() {
    if [ -n "$TMUX" ] &&
        { [ "${TERM%%-*}" = "tmux" ] ||
            [ "${TERM%%-*}" = "screen" ]; }; then
        # Tell tmux to pass the escape sequences through
        printf "\ePtmux;\e\e]%s\007\e\\" "$1"
    elif [ "${TERM%%-*}" = "screen" ]; then
        # GNU screen (screen, screen-256color, screen-256color-bce)
        printf "\eP\e]%s\007\e\\" "$1"
    else
        printf "\e]%s\e\\" "$1"
    fi
}

# has_cmd fzf && {
#     export FZF_DEFAULT_OPTS='--height 40% --layout=reverse --border --prompt="> "'
#     export FZF_CTRL_T_COMMAND="fd --type f --hidden --follow --exclude .git"
#     export FZF_ALT_C_COMMAND="fd --type d --hidden --follow --exclude .git"
#     export FZF_DEFAULT_COMMAND="fd --type f --hidden --follow --exclude .git"
# }
