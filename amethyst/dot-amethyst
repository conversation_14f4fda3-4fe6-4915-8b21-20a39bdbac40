{"LAYOUTS": "----------------------", "layouts": ["tall", "tall-right", "middle-wide", "fullscreen"], "MODIFIERS": "----------------------", "Valid modifiers are": ["option", "shift", "control", "command"], "mod1": ["control", "command"], "mod2": ["command", "shift"], "COMMANDS": "----------------------", "Commands are": {"cycle-layout": "Cycle layout to the next layout", "cycle-layout-backward": "Cycle layout to the previous layout", "focus-screen-1": "Focus the main window on the first screen", "focus-screen-2": "Focus the main window on the second screen", "focus-screen-3": "Focus the main window on the third screen", "focus-screen-4": "Focus the main window on the fourth screen", "throw-screen-1": "Throw the focused window to the first screen", "throw-screen-2": "Throw the focused window to the second screen", "throw-screen-3": "Throw the focused window to the third screen", "throw-screen-4": "Throw the focused window to the fourth screen", "shrink-main": "Shrink the main pane of the current layout", "expand-main": "Expand the main pane of the current layout", "increase-main": "Increase the number of windows in the main pane", "decrease-main": "Decrease the number of windows in the main pane", "focus-ccw": "Move window focus counter-clockwise on the current screen", "focus-cw": "Move window focus clockwise on the current screen", "swap-ccw": "<PERSON>wap focused window with the next window going counter-clockwise", "swap-cw": "Swap focused window with the next window going clockwise", "swap-main": "Swap focused window with the main window of its screen", "throw-space-1": "Throw the focused window to the first space", "throw-space-2": "Throw the focused window to the second space", "throw-space-3": "Throw the focused window to the third space", "throw-space-4": "Throw the focused window to the fourth space", "throw-space-5": "Throw the focused window to the fifth space", "throw-space-6": "Throw the focused window to the sixth space", "throw-space-7": "Throw the focused window to the seventh space", "throw-space-8": "Throw the focused window to the eighth space", "throw-space-9": "Throw the focused window to the ninth space", "toggle-float": "Toggle the focused window between being floating and tiled"}, "screens": "4", "cycle-layout": {"mod": "mod2", "key": "space"}, "cycle-layout-backward": {"mod": "mod2", "key": "m"}, "select-tall-right-layout": {"mod": "mod1", "key": "a"}, "select-tall-layout": {"mod": "mod1", "key": "s"}, "select-fullscreen-layout": {"mod": "mod1", "key": "d"}, "select-column-layout": {"mod": "mod1", "key": "f"}, "focus-screen-1": {"mod": "mod1", "key": "w"}, "focus-screen-2": {"mod": "mod1", "key": "e"}, "focus-screen-3": {"mod": "mod1", "key": "r"}, "focus-screen-4": {"mod": "mod1", "key": "q"}, "throw-screen-1": {"mod": "mod2", "key": "w"}, "throw-screen-2": {"mod": "mod2", "key": "e"}, "throw-screen-3": {"mod": "mod2", "key": "r"}, "throw-screen-4": {"mod": "mod2", "key": "q"}, "shrink-main": {"mod": "mod1", "key": "h"}, "expand-main": {"mod": "mod1", "key": "l"}, "increase-main": {"mod": "mod1", "key": ","}, "decrease-main": {"mod": "mod1", "key": "."}, "focus-ccw": {"mod": "mod1", "key": "j"}, "focus-cw": {"mod": "mod1", "key": "k"}, "swap-screen-ccw": {"mod": "mod2", "key": "h"}, "swap-screen-cw": {"mod": "mod2", "key": "l"}, "swap-ccw": {"mod": "mod2", "key": "j"}, "swap-cw": {"mod": "mod2", "key": "k"}, "swap-main": {"mod": "mod1", "key": "enter"}, "throw-space-1": {"mod": "mod2", "key": "1"}, "throw-space-2": {"mod": "mod2", "key": "2"}, "throw-space-3": {"mod": "mod2", "key": "3"}, "throw-space-4": {"mod": "mod2", "key": "4"}, "throw-space-5": {"mod": "mod2", "key": "5"}, "throw-space-6": {"mod": "mod2", "key": "6"}, "throw-space-7": {"mod": "mod2", "key": "7"}, "throw-space-8": {"mod": "mod2", "key": "8"}, "throw-space-9": {"mod": "mod2", "key": "9"}, "toggle-float": {"mod": "mod1", "key": "t"}, "toggle-tiling": {"mod": "mod2", "key": "t"}, "display-current-layout": {"mod": "mod1", "key": "i"}, "MISC": "----------------------", "floating": ["com.apple.systempreferences"], "float-small-windows": true, "mouse-follows-focus": false, "focus-follows-mouse": false, "enables-layout-hud": true, "enables-layout-hud-on-space-change": true}