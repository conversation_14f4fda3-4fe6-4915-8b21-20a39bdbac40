# Changing the prefix from C-b to `
set -g prefix `
# Free the original Ctrl-b prefix keybinding
unbind C-b
# Ensure that we can send ` to other apps by double tapping ``
bind ` send-prefix

# Set the base index for windows to 1 instead of 0
set -g base-index 1

# Set the base index for panes to 1 instead of 0
setw -g pane-base-index 1
set -g history-limit 1000000     # increase history size (from 2,000)
set -g renumber-windows on       # renumber all windows when any window is closed
set -g set-clipboard on          # use system clipboard
set -g detach-on-destroy off     # don't exit from tmux when closing a session
set -g status-position top       # macOS / darwin style
set -g default-terminal "${TERM}"
set-option -g exit-empty off

# Reload the file with Prefix r
bind r source-file ~/.config/tmux/tmux.conf \; display "Reloaded!"

# http://unix.stackexchange.com/questions/14300/tmux-move-window-to-pane
bind-key B command-prompt -p "join pane from window:"  "join-pane -s ':%%'"
bind-key S command-prompt -p "send pane to window:"  "join-pane -t ':%%'"

# Quick pane selection
bind -r C-h select-window -t :-
bind -r C-l select-window -t :+

# wayland
# bind-key -T copy-mode y send-keys -X copy-pipe-and-cancel "wl-copy && wl-paste -n | wl-copy -p"
# bind-key C-y run "wl-paste -n | tmux load-buffer - ; tmux paste-buffer"

# x11
# Move between tmux panes and emacs windows easily
bind -n S-Left run "(tmux display-message -p '#{pane_current_command}' | grep -iqE '(^|\/)n?vim(diff)?$|emacs.*$' && tmux send-keys S-Left) || tmux select-pane -L"
bind -n S-Down run "(tmux display-message -p '#{pane_current_command}' | grep -iqE '(^|\/)n?vim(diff)?$|emacs.*$' && tmux send-keys S-Down) || tmux select-pane -D"
bind -n S-Up run "(tmux display-message -p '#{pane_current_command}' | grep -iqE '(^|\/)n?vim(diff)?$|emacs.*$' && tmux send-keys S-Up) || tmux select-pane -U"
bind -n S-Right run "(tmux display-message -p '#{pane_current_command}' | grep -iqE '(^|\/)n?vim(diff)?$|emacs.*$' && tmux send-keys S-Right) || tmux select-pane -R"

# key binding for splitting panes
bind = split-window -h
# bind - split-window -v

# # mouse support - set to on if you want to use the mouse
setw -g mouse on

# Set the default terminal mode to 256color mode
set -g default-terminal "screen-256color"

# enable activity alerts
setw -g monitor-activity on
set -g visual-activity on

# enable vi keys.
setw -g mode-keys emacs

# enable this if you change above mode-keys to vi
# set -g @shell_mode 'vi'

# allow xterm keybindings(eg S-Left) in apps like emacs
set-window-option -g xterm-keys on

# True Color support
set-option -ga terminal-overrides ",xterm-256color:Tc"

#####################
###    PLUGINS    ###
#####################

set -g @plugin 'tmux-plugins/tpm'

# set -g pane-active-border-style 'fg=magenta,bg=default'
# set -g pane-border-style 'fg=brightblack,bg=default'

set -g @fzf-url-fzf-options '-p 60%,30% --prompt="   " --border-label=" Open URL "'
set -g @fzf-url-history-limit '2000'

set -g set-titles on
set -g set-titles-string '#I:#W'

# catppuccin theme
set -g @plugin 'catppuccin/tmux#latest' # See https://github.com/catppuccin/tmux/tags for additional tags
set -g @catppuccin_flavor 'mocha'
set -g @catppuccin_window_status_style "rounded"

set -g status-right-length 100
set -g status-left-length 100
set -g status-left ""
set -g status-right "#{E:@catppuccin_status_session}"
set -g @catppuccin_window_number_position "right"
set -g @catppuccin_window_current_text "#W#{?window_zoomed_flag,(),}"
# set -agF status-right "#{E:@catppuccin_status_cpu}"
# set -ag status-right "#{E:@catppuccin_status_uptime}"
# set -agF status-right "#{E:@catppuccin_status_battery}"

# # theme
# set -g @plugin 'arcticicestudio/nord-tmux'
# set -g @plugin 'tmux-plugins/tmux-prefix-highlight'

set -g @plugin 'nhdaly/tmux-better-mouse-mode'

# bindings
set -g @plugin 'tmux-plugins/tmux-sensible'
set -g @plugin 'tmux-plugins/tmux-pain-control'

set -g @plugin 'fcsonline/tmux-thumbs'
set -g @plugin 'sainnhe/tmux-fzf'
set -g @plugin 'wfxr/tmux-fzf-url'

# # copy/paste
# set -g @plugin 'tmux-plugins/tmux-copycat'
set -g @plugin 'tmux-plugins/tmux-yank'

# open stuff
# set -g @plugin 'tmux-plugins/tmux-open'
# set -g @plugin 'tmux-plugins/tmux-urlview'

set -g @plugin 'christoomey/vim-tmux-navigator'

set-environment -g TMUX_PLUGIN_MANAGER_PATH '~/.tmux'

if "test ! -d ~/.tmux/tpm" \
   "run 'echo \"Installing tpm...\";git clone https://github.com/tmux-plugins/tpm ~/.tmux/tpm && ~/.tmux/tpm/bin/install_plugins'"

run '~/.tmux/tpm/tpm'
