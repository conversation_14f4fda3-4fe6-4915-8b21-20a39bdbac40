# Linux Tools

## Common Linux Tools

### GNU Stow

You should always use [dotfiles](https://dotfiles.github.io/) to maintain all your configurations. [Gnu Stow](https://www.gnu.org/software/stow/) is really simple to get started. It's what I still use. Other more sophisticated options are [chezmoi](https://www.chezmoi.io) and [yadm](https://yadm.io)

Learn Gnu Stow with [NEVER lose dotfiles again with GNU Stow](https://youtu.be/NoFiYOqnC4o) and/or [~/.dotfiles 101: A Zero to Configuration Hero Blueprint](https://youtu.be/WpQ5YiM7rD4)

### Neovim

DO NOT try to configure neovim from scratch. First learn vim and neovim using some predefined configuration. For beginners, one of the best is [Neovim Kickstart](https://github.com/nvim-lua/kickstart.nvim).

If you are already familiar with vim, then use [LazyVim](https://github.com/LazyVim/LazyVim).

1. [ThePrimeagen Vim Tutorial](https://www.youtube.com/playlist?list=PLm323Lc7iSW_wuxqmKx_xxNtJC_hJbQ7R) - If you don't know vim, start here.

2. [Complete Neovim Setup Guide - TJ](https://www.youtube.com/watch?v=m8C0Cq9Uv9o) - Once you know vim wekk, then understand how to configure neovim by learning [kickstart neovim config](https://github.com/nvim-lua/kickstart.nvim). You could also use [Neovim for Newbs](https://www.youtube.com/playlist?list=PLsz00TDipIffreIaUNk64KxTIkQaGguqn) and [Neovim from scratch](https://www.youtube.com/playlist?list=PLsz00TDipIffxsNXSkskknolKShdbcALR).

Learn about `LazyVim` using [LazyVim: Zero to IDE](https://www.youtube.com/playlist?list=PLXJ0on0Dau77THnZeQLqK1hV6s2udsaAm)

### Tmux

1. [Tmux Basics](https://www.youtube.com/watch?v=niuOc02Rvrc)

2. [Tmux from scratch](https://www.youtube.com/watch?v=GH3kpsbbERo)
