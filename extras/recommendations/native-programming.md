# Native Development

## Linux Programming

1. [The Linux Programming Interface](https://www.amazon.com/Linux-Programming-Interface-System-Handbook/dp/1593272200) - Pick the topic you are interested in and read the corresponding chapter.

2. [Systems Performance - Brendan <PERSON>](https://www.amazon.com/Systems-Performance-Brendan-<PERSON>/dp/0136820158/133-7213979-6336830)

3. [The TCP/IP Guide](https://www.amazon.com/TCP-Guide-Comprehensive-Illustrated-Protocols/dp/159327047X) - Reference book on TCP/IP stack.

4. [Linux System Programming](https://www.amazon.com/Linux-System-Programming-Talking-Directly/dp/1449339530) - A bit dated, but still one of the best books on Linux system programming.

## C++

1. Value based programming

2. RAII

3. Smart Pointers

4. Error handling

5. Option

6. Move Semantics

7. r value semantics

8. auto

9. New auto based templates

10. Operator overloading

11. Namespaces

12. Big five
