# PC/MAC Hardware Recommendations

## Keyboards

### Split Keyboards

Generally split keyboards are preferrable for touch typists. Most split keyboards are ergonomic. Usually takes time to get used to.

1. [Glove 80](https://www.moergo.com/) - This is the best ergonomic split keyboard but only for touch typists. It will take a really long time to get used to.

2. [Voyager](https://www.zsa.io/voyager) - Very portable, less ergonomic, very popular.

3. [Moonlander](https://www.zsa.io/moonlander) - Easy to get used to, currently the most poular.

4. [Dygma Defy](https://dygma.com/pages/defy) - A lot of moonlander users are switching to this.

If you want to spend least amount of time learning a new keyboard layout, or if you are not a touch typist, or want a cheaper alternative.

1. [Keychron Q11](https://www.keychron.com/products/keychron-q11-qmk-custom-mechanical-keyboard) - If you end up not liking split keyboard, you could just use this as a normal keyboard too.

2. [RKS70](https://www.amazon.com/RK-ROYAL-KLUDGE-Bluetooth-Mechanical/dp/B0C88V7LQK) - Maybe the best budget split keyboard.

Keys are a personal preference, most popular are cherry mx blue, red, brown.

- Blue would be very loud and clicky.

- Red will be relatively more silent and is considered best for gamers and programmers. Red are also more closer to common membrane keyboards and some people hate these.

- Brown if you like a bit clicky compared to Red but almost as silent as red. This is the safest choice, if it's your first mechanical keyboard.

My personal preference is Cherry MX Red.

### Mouse

Mouse is always a personal choice, you might hate what's considered the best. Always try before buying.

1. [Logitech MX Master 3s](https://www.amazon.com/Logitech-MX-Master-3S-Pale/dp/B09HMKFDXC) - If you like heave mouse. Not good for gamers. Otherwise this is the most popular mouse. I don't like this mouse much, a bit too heavy.

2. [Logitech G Pro X](https://www.amazon.com/Logitech-Superlight-Lightspeed-Lightweight-Programmable/dp/B09NBWL8J5) - Lightweight, Wireless, most popular for gamers. I love this mouse. Ambidextrous, really useful if you suspect your RSI is due to your mouse usage.

3. [Logitech G 305](https://www.amazon.com/Logitech-LIGHTSPEED-Wireless-Gaming-Mouse/dp/B07CMS5Q6N) - Similar to Pro X, lot cheaper, but with AA battery. Not as lightweight. Ambidextrous.

4. [Logitech G203](https://www.amazon.com/Logitech-LIGHTSYNC-Wired-Gaming-Mouse/dp/B07YN82X3B) - Really cheap. But really good. I use it with all of my secondary setups.

5. [Logitech MX Vertical](https://www.amazon.com/Logitech-Vertical-Wireless-Mouse-Rechargeable/dp/B07FNJB8TT) - If you suspect your RSI is due to your mouse usage. Currently, I don't use this.

### Laptop

1. [Asus Zenbook S14](https://www.asus.com/in/laptops/for-home/zenbook/asus-zenbook-s-14-ux5406/) - Lightweight, good performance, very good battery life.

2. [Asus Zephyrus G16](https://rog.asus.com/in/laptops/rog-zephyrus/rog-zephyrus-g16-2024-ga605/) - Great performance, good battery life, reasonable weight.

3. [Asus Flow Z13 - Ryzen 9 AI Max+ 395](https://rog.asus.com/laptops/rog-flow/rog-flow-z13-2025/) - Best performance, lightweight, good battery life. This is not released yet, might support 128GB in some regions. State of the art CPU.

If you can buy and use a macbook, then there's no competition. Just use a macbook. Especially [Macbook Air 15 inch](https://www.apple.com/shop/buy-mac/macbook-air/15-inch-sky-blue-m4-chip-with-10-core-cpu-10-core-gpu-16gb-memory-256gb). Nothing can beat this in price or performance especially when you factor in battery life.

### Desktop

If you are a programmer, then just buy either a [Mac Studio](https://www.apple.com/mac-studio/) or a [Mac mini](https://www.apple.com/mac-mini/) based on your budget. Buying a PC with nvidia GPU is not worth it.

If you don't care about LLMs, then you could buy the following.

1. [Minisforum BD795i SE](https://www.amazon.com/MINISFORUM-Motherboard-Channel-PCIe5-0x16-Graphics/dp/B0CNPZ874Y) - Nothing beats this in value, has 16 cores and near 7950x performance.

2. [Crucual 2x48GB](https://www.amazon.com/Crucial-2x48GB-5600MT-5200MT-CT2K48G56C46S5/dp/B0C79K5VGZ)

3. [Samsung 990 Pro](https://www.amazon.com/SAMSUNG-Workstations-Compatible-Playstation-MZ-V9P4T0CW/dp/B0CHHFR1LG)

4. [Corsair SF850 SF 3.1](https://www.amazon.com/CORSAIR-SF850-Modular-Platinum-Supply/dp/B0D45PQ8C4)

5. [Any mini itx case](https://www.newegg.com/matcha-green-thermaltake-tr100-mini-itx/p/N82E16811133560)

Or If you prefer an ATX/mATX desktop

1. [Ryzen 9 9950x3D](https://www.newegg.com/amd-ryzen-9-9950x3d-ryzen-9-9000-series-granite-ridge-socket-am5-processor/p/N82E16819113884)

2. [Any X870e motherboard](https://www.newegg.com/p/N82E16813144666) or [Any B650 micro atx motherboard](https://www.newegg.com/gigabyte-b650m-gaming-plus-wifi-micro-atx-amd-b650-am5/p/N82E16813145502) should work.

3. [Corsair RM1000x](https://www.amazon.com/CORSAIR-RM1000x-Modular-Low-Noise-Supply/dp/B0DJ1M9C62)

4. [Crucial Pro 6400 DDR5 2x48GB](https://www.amazon.com/Crucial-6000MHz-Overclocking-Desktop-Compatible/dp/B0DSQMCV8B)

5. If you need a GPU consider `AMD 9070 XT`. AMD GPUs works better than nvidia on linux. If you work with 3D models and/or blender, then nvidia is better. Consider something like `4070 Ti Super` or `5070 Ti`. Note that these aren't useful for LLMs.

You could also buy a mini PC instead of a desktop, especially if you don't need more than 96GB RAM.

1. [Minisforum 795S7 mini itx system](https://www.amazon.com/MINISFORUM-795S7-Barebone-Display-Computer/dp/B0DJRD59BX)

2. [Lenovo m75q Gen 5](https://www.lenovo.com/in/en/p/desktops/thinkcentre/m-series-tiny/lenovo-thinkcentre-m75q-gen-5-tiny-amd/12rrcto1wwin1)

3. [GMKtec HX 370](https://www.amazon.com/GMKtec-Computers-LPDDR5X-Support-Interface/dp/B0DS5XLRVQ)

Common PC components

#### PC Case

1. [Lian Li A3 mATX](https://www.amazon.com/Lian-Chassis-Flexible-Installation-No-Included-Supports-Collaboration-White/dp/B0D5HCYZXR)

2. [Thermaltake TR100](https://www.thermaltake.com/tr100-mini-tower-chassis.html)

3. [Fractal Design Torrent](https://www.amazon.com/Fractal-Design-Torrent-Black-Light/dp/B08699NR75)

#### Monitor

Not everyone gets used to 32 inch monitor. So it's not safe unless you have tried it.

1. [Benq RD320U](https://www.amazon.com/BenQ-RD320U-3840x2160-Programming-Monitor/dp/B0DFD2Q8F1) or [Benq RD280U](https://www.amazon.com/BenQ-RD280U-Programming-Fine-Coated-Eco-Friendly/dp/B0D2PDYHD9)

2. [Benq PD3205UA](https://www.amazon.com/BenQ-PD3205UA-Mac-Ready-Uniformity-Calibration/dp/B0BTTVM832) or [Benq PD2725U](https://www.amazon.com/BenQ-PD2725U-Thunderbolt-Mac-Ready-Accurate/dp/B0918QXTGN)

#### Speakers

1. [Kanto YU2GT](https://www.amazon.com/Kanto-Composite-Amplifier-Subwoofer-YU2WALNUT/dp/B00GMPDB1W) - Reasonable sound, compact.

#### Microphone

1. [Samson Q2U](https://www.amazon.com/Samson-Handheld-Microphone-Recording-Podcasting/dp/B001R747SG) - I highly recommend dynamic microphones. This is the cheapest one that sounds great too.
