[project]
name = "install-packages"
version = "0.1.0"
description = "A tool to install packages from YAML definitions across multiple package managers"
dependencies = ["pyyaml>=6.0.1", "typing-extensions>=4.8.0", "pydantic>=2.0.0"]
requires-python = ">=3.10"
license = { text = "MIT" }
keywords = ["package-manager", "installer", "yaml"]

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[project.scripts]
install-packages = "install_packages:main"

[tool.hatch.metadata]
allow-direct-references = true

[tool.hatch.build.targets.wheel]
packages = ["install_packages"]

[project.optional-dependencies]
dev = ["ruff>=0.3.0", "mypy>=1.9.0", "pyright>=1.1.352"]

[tool.ruff]
target-version = "py310"
line-length = 100
fix = true
unsafe-fixes = false

[tool.ruff.lint]
select = [
    "E",   # pycodestyle
    "F",   # pyflakes
    "I",   # isort
    "RUF", # ruff-specific rules
]

[tool.ruff.lint.isort]
known-first-party = ["install_packages"]
combine-as-imports = true

[tool.mypy]
python_version = "3.10"
strict = true
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
