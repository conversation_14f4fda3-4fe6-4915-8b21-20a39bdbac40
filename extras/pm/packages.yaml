apt:
  pre:
    - "curl -fsSL https://deb.nodesource.com/setup_lts.x | sudo -E bash -"
    - "curl -fsSL https://download.docker.com/linux/ubuntu/gpg | sudo apt-key add -"
  packages:
    - vim
    - nodejs
    - docker-ce
  post:
    - "sudo usermod -aG docker $USER"
    - "sudo systemctl enable docker"

brew:
  packages:
    - git
    - node
    - python@3.11
  post:
    - "npm install -g typescript"
    - "pip3 install --user pipx"

pixi:
  packages:
    - python
    - rust

dnf:
  packages:
    - gcc
    - make

zypper:
  packages:
    - gcc
    - make

stow:
  packages:
    - bash
    - zsh

pacman:
  packages:
    - base-devel
    - git

flatpak:
  packages:
    - org.mozilla.firefox
    - com.visualstudio.code

cargo:
  packages:
    - ripgrep
    - bat
  post:
    - "bat cache --build"

pip:
  packages:
    - requests
    - pandas

npm:
  packages:
    - typescript
    - yarn

pnpm:
  packages:
    - vite
    - degit

go:
  packages:
    - golang.org/x/tools/cmd/goimports@latest
    - github.com/junegunn/fzf@latest
