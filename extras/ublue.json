{"all": {"include": {"all": ["adcli", "adw-gtk3-theme", "bash-color-prompt", "bcache-tools", "bluefin-backgrounds", "bluefin-schemas", "bor<PERSON><PERSON><PERSON>", "bootc", "bluefin-cli-logos", "bluefin-fastfetch", "bluefin-faces", "ddcutil", "evtest", "epson-inkjet-printer-escpr", "epson-inkjet-printer-escpr2", "fastfetch", "fish", "firewall-config", "foo2zjs", "fuse-encfs", "gcc", "git-credential-libsecret", "glow", "gum", "hplip", "krb5-workstation", "ibus-mozc", "ifuse", "input-leap", "input-remapper", "jetbrains-mono-fonts-all", "libimobiledevice", "libxcrypt-compat", "libsss_autofs", "lm_sensors", "make", "mesa-libGLU", "mozc", "nerd-fonts", "<PERSON><PERSON><PERSON>-<PERSON><PERSON><PERSON><PERSON>", "opendyslexic-fonts", "printer-driver-<PERSON><PERSON><PERSON>", "pulseaudio-utils", "powertop", "python3-pip", "python3-pygit2", "rclone", "restic", "samba-dcerpc", "samba-ldb-ldap-modules", "samba-winbind-clients", "samba-winbind-modules", "samba", "setools-console", "solaar", "sssd-ad", "sssd-krb5", "sssd-nfs-idmap", "stress-ng", "tailscale", "tmux", "topgrade", "ublue-fastfetch", "ublue-motd", "usbmuxd", "ublue-brew", "ublue-bling", "wireguard-tools", "xprop", "wl-clipboard", "zsh"], "silverblue": ["cryfs", "gnome-shell-extension-appindicator", "gnome-shell-extension-blur-my-shell", "gnome-shell-extension-caffeine", "gnome-shell-extension-dash-to-dock", "gnome-shell-extension-gsconnect", "gnome-shell-extension-logo-menu", "gnome-shell-extension-search-light", "gnome-shell-extension-tailscale-gnome-qs", "libgda", "libgda-sqlite", "libratbag-ratbagd", "nautilus-gsconnect", "nautilus-open-any-terminal", "openssh-askpass", "simple-scan", "yaru-theme", "zenity"], "dx": ["adobe-source-code-pro-fonts", "android-tools", "bcc", "bpftop", "bpftrace", "cascadia-code-fonts", "cockpit-bridge", "cockpit-machines", "cockpit-networkmanager", "cockpit-ostree", "cockpit-podman", "cockpit-selinux", "cockpit-storaged", "cockpit-system", "code", "containerd.io", "dbus-x11", "devpod", "docker-ce", "docker-ce-cli", "docker-buildx-plugin", "docker-compose-plugin", "edk2-ovmf", "flatpak-builder", "genisoimage", "google-droid-sans-mono-fonts", "google-go-mono-fonts", "ibm-plex-mono-fonts", "incus", "incus-agent", "iotop", "kcli", "libvirt", "libvirt-nss", "lxc", "mozilla-fira-mono-fonts", "nicstat", "numactl", "osbuild-selinux", "p7zip-plugins", "p7zip", "podman-bootc", "podman-compose", "podman-machine", "podman-tui", "pod<PERSON><PERSON>", "powerline-fonts", "python3-<PERSON><PERSON><PERSON>", "qemu-char-spice", "qemu-device-display-virtio-gpu", "qemu-device-display-virtio-vga", "qemu-device-usb-redirect", "qemu-img", "qemu-system-x86-core", "qemu-user-binfmt", "qemu-user-static", "qemu", "rocm-hip", "rocm-opencl", "rocm-smi", "sysprof", "tiptop", "trace-cmd", "ubuntu-family-fonts", "udica", "umoci", "virt-manager", "virt-viewer", "virt-v2v", "ydotool"]}, "exclude": {"all": ["firefox-langpacks", "firefox"], "silverblue": ["gnome-extensions-app", "gnome-shell-extension-background-logo", "gnome-software-rpm-ostree", "gnome-terminal-nautilus"], "dx": []}}, "40": {"include": {"all": ["ptyxis"], "silverblue": [], "dx": ["lxd-agent", "lxd"]}, "exclude": {"all": [], "silverblue": [], "dx": []}}, "41": {"include": {"all": ["google-noto-fonts-all"], "silverblue": []}, "exclude": {"all": [], "silverblue": [], "dx": []}}}