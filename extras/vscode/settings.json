{
  "[json,jsonc,javascript,typescript,javacscriptreact,typescriptreact,css,html]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode",
    "editor.suggestSelection": "recentlyUsed"
  },
  "[txt]": {
    "editor.formatOnSave": false,
    "editor.formatOnType": false
  },
  "breadcrumbs.filePath": "on",
  "cmake.options.statusBarVisibility": "visible",
  "codestream.serverUrl": "https://codestream-us1.service.newrelic.com",
  "console-ninja.featureSet": "Pro",
  "console-ninja.toolsToEnableSupportAutomaticallyFor": {
    "cypress": false,
    "hydrogen": false,
    "nuxt": false,
    "qwik": false
  },
  "cSpell.enabled": false,
  "debug.javascript.codelens.npmScripts": "never",
  "diffEditor.hideUnchangedRegions.enabled": true,
  "diffEditor.ignoreTrimWhitespace": false,
  "editor.acceptSuggestionOnCommitCharacter": false,
  "editor.acceptSuggestionOnEnter": "smart",
  "editor.autoIndent": "advanced",
  "editor.cursorSmoothCaretAnimation": "on",
  "editor.detectIndentation": true,
  "editor.emptySelectionClipboard": false,
  "editor.find.autoFindInSelection": "never",
  "editor.fontFamily": "'JetbrainsMono Nerd Font Mono', 'Jetbrains Mono', 'MonaspiceRn Nerd Font Mono', 'Cascadia Mono', 'Monaco', 'Consolas', 'Fira Code Mono', 'Ubuntu Mono', 'Source Code Pro', 'Droid Sans Mono', 'monospace'",
  "editor.fontLigatures": true,
  "editor.fontSize": 15,
  "editor.formatOnPaste": true,
  "editor.formatOnSave": true,
  "editor.glyphMargin": false,
  "editor.gotoLocation.multipleDefinitions": "goto",
  "editor.gotoLocation.multipleImplementations": "goto",
  "editor.guides.indentation": false,
  "editor.indentSize": 2,
  "editor.inlineSuggest.enabled": true,
  "editor.insertSpaces": true,
  "editor.lightbulb.enabled": "off",
  "editor.lineNumbers": "relative",
  "editor.linkedEditing": true,
  "editor.minimap.enabled": false,
  "editor.multiCursorModifier": "ctrlCmd",
  "editor.parameterHints.cycle": true,
  "editor.renderWhitespace": "boundary",
  "editor.rulers": [
    80
  ],
  "editor.smoothScrolling": true,
  "editor.snippetSuggestions": "bottom",
  "editor.suggest.localityBonus": true,
  "editor.suggestSelection": "first",
  "editor.tabCompletion": "on",
  "editor.tabSize": 2,
  "editor.trimAutoWhitespace": true,
  "editor.wordBasedSuggestions": "off",
  "editor.wordWrap": "on",
  "emmet.includeLanguages": {
    "javascript": "javascriptreact",
    "plaintext": "jade",
    "vue-html": "html"
  },
  "emmet.showAbbreviationSuggestions": false,
  "emmet.showExpandedAbbreviation": "never",
  "emmet.syntaxProfiles": {
    "javascript": "html"
  },
  "emmet.triggerExpansionOnTab": true,
  "eslint.validate": [
    "javascript",
    "javascriptreact",
    "typescript",
    "typescriptreact"
  ],
  "explorer.compactFolders": true,
  "explorer.confirmDelete": false,
  "explorer.confirmDragAndDrop": false,
  "extensions.autoUpdate": true,
  "files.associations": {
    "*.md": "mdx"
  },
  "files.autoSave": "onFocusChange",
  "files.defaultLanguage": "{activeEditorLanguage}",
  "files.exclude": {
    "USE_GITIGNORE": true
  },
  "files.insertFinalNewline": true,
  "files.trimTrailingWhitespace": true,
  "git.autofetch": true,
  "git.openRepositoryInParentFolders": "always",
  "github.copilot.enable": {
    "*": true,
    "markdown": true,
    "plaintext": false,
    "scminput": false
  },
  "githubPullRequests.createOnPublishBranch": "never",
  "GitLive.Issue tracker integration": "Disabled",
  "GitLive.Special branches": "main|master|trunk|dev|develop|qa|test|release[-/].*",
  "javascript.format.semicolons": "remove",
  "javascript.suggest.completeFunctionCalls": false,
  "javascript.updateImportsOnFileMove.enabled": "always",
  "javascript.validate.enable": false,
  "npm.packageManager": "pnpm",
  "nxConsole.showNodeVersionOnStartup": false,
  "playwright.reuseBrowser": true,
  "problems.showCurrentInStatus": true,
  "remote.SSH.remotePlatform": {
    "************": "linux"
  },
  "search.exclude": {
    "**/.build": true,
    "**/.gh-pages": true,
    "**/.history": true,
    "**/bower_components": true,
    "**/build": true,
    "**/coverage": true,
    "**/dist": true,
    "**/node_modules": true,
    ".parcel-cache": true,
    "USE_GITIGNORE": true
  },
  "sonarlint.rules": {
    "typescript:S107": {
      "level": "off"
    },
    "typescript:S125": {
      "level": "off"
    },
    "typescript:S2814": {
      "level": "off"
    },
    "typescript:S3358": {
      "level": "off"
    },
    "typescript:S3863": {
      "level": "off"
    },
    "typescript:S4325": {
      "level": "off"
    }
  },
  "terminal.integrated.env.linux": {},
  "terminal.integrated.env.windows": {},
  "typescript.disableAutomaticTypeAcquisition": true,
  "typescript.format.semicolons": "remove",
  "typescript.preferences.importModuleSpecifier": "non-relative",
  "typescript.suggest.completeFunctionCalls": false,
  "typescript.updateImportsOnFileMove.enabled": "always",
  "window.commandCenter": true,
  "window.newWindowDimensions": "inherit",
  "window.restoreFullscreen": true,
  "window.restoreWindows": "one",
  "workbench.colorTheme": "GitHub Dark",
  "workbench.commandPalette.preserveInput": true,
  "workbench.editor.enablePreviewFromQuickOpen": true,
  "workbench.editor.highlightModifiedTabs": true,
  "workbench.editor.limit.enabled": true,
  "workbench.editor.limit.perEditorGroup": false,
  "workbench.editor.limit.value": 10,
  "workbench.editor.showTabs": "multiple",
  "workbench.editorAssociations": {
    "*.md": "vscode.markdown.preview.editor"
  },
  "workbench.panel.defaultLocation": "bottom",
  "workbench.settings.editor": "ui",
  "workbench.settings.openDefaultKeybindings": false,
  "workbench.settings.openDefaultSettings": false,
  "workbench.sideBar.location": "right",
  "workbench.startupEditor": "none",
  "workbench.statusBar.visible": true
  // "terminal.integrated.defaultProfile.linux": "bash",
  // "terminal.integrated.profiles.linux": {
  //   "bash": {
  //     "path": "host-spawn",
  //     "args": ["bash"]
  //   }
  // }
}
