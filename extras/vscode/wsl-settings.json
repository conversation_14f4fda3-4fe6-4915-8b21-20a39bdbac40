{"editor.fontFamily": "'JetbrainsMono Nerd Font Mono', 'Jetbrains Mono', 'MonaspiceRn Nerd Font Mono', 'Cascadia Mono', 'Monaco', 'Consolas', 'Fira Code Mono', 'Ubuntu Mono', 'Source Code Pro', 'Droid Sans Mono', 'monospace'", "editor.fontSize": 15, "editor.acceptSuggestionOnEnter": "smart", "editor.autoIndent": "advanced", "editor.cursorSmoothCaretAnimation": "on", "editor.detectIndentation": true, "editor.emptySelectionClipboard": false, "editor.find.autoFindInSelection": "never", "editor.formatOnPaste": true, "editor.formatOnSave": true, "editor.gotoLocation.multipleDefinitions": "goto", "editor.gotoLocation.multipleImplementations": "goto", "editor.guides.indentation": false, "editor.indentSize": 2, "editor.inlineSuggest.enabled": true, "editor.insertSpaces": true, "editor.lineNumbers": "relative", "editor.linkedEditing": true, "editor.multiCursorModifier": "ctrlCmd", "editor.parameterHints.cycle": true, "editor.renderWhitespace": "boundary", "editor.rulers": [80], "remote.extensionKind": {"asvetliakov.vscode-neovim": ["workspace"]}, "update.mode": "manual", "editor.smoothScrolling": true, "editor.snippetSuggestions": "bottom", "editor.suggest.localityBonus": true, "editor.suggestSelection": "first", "editor.tabCompletion": "on", "editor.tabSize": 2, "editor.trimAutoWhitespace": true, "editor.wordBasedSuggestions": "off", "editor.wordWrap": "on", "files.autoSave": "onFocusChange", "files.insertFinalNewline": true, "files.trimTrailingWhitespace": true, "git.openRepositoryInParentFolders": "always", "terminal.integrated.fontSize": 14, "editor.minimap.enabled": false, "git.autofetch": true, "window.commandCenter": true, "workbench.commandPalette.preserveInput": true, "workbench.editor.highlightModifiedTabs": true, "workbench.colorTheme": "<PERSON><PERSON><PERSON><PERSON>", "workbench.iconTheme": "catppuccin-mocha", "breadcrumbs.enabled": false, "editor.folding": false, "editor.glyphMargin": false, "workbench.sideBar.location": "right", "window.menuBarVisibility": "toggle"}