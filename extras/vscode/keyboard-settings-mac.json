// Place your key bindings in this file to override the defaults
[
  {
    "key": "ctrl+left",
    "command": "cursorWordLeft",
    "when": "textInputFocus"
  },
  {
    "key": "alt+left",
    "command": "-cursorWordLeft",
    "when": "textInputFocus"
  },
  {
    "key": "ctrl+right",
    "command": "cursorWordRight"
  },
  {
    "key": "ctrl+shift+right",
    "command": "cursorWordRightSelect"
  },
  {
    "key": "ctrl+shift+left",
    "command": "cursorWordLeftSelect",
    "when": "textInputFocus"
  },
  {
    "key": "shift+alt+left",
    "command": "-cursorWordLeftSelect",
    "when": "textInputFocus"
  }
]
