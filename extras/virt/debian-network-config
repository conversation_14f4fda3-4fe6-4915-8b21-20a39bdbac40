# /var/lib/libvirt/boot/cloud-init/debian-network-config
# Network configuration using cloud-init network config format v2 (similar to netplan)

version: 2
ethernets:
  # Assumes the VirtIO NIC appears as 'eth0'. Check inside the VM with 'ip a' if needed.
  eth0:
    dhcp4: true
    # Optional: Enable DHCPv6 if needed
    # dhcp6: true
    # Optional: Explicitly disable IPv6 if not used
    # dhcp6: false

  # --- Example Static Configuration (Uncomment and customize if needed) ---
  # eth0:
  #   dhcp4: false
  #   dhcp6: false
  #   addresses: [***********50/24] # Set your desired static IP and subnet mask
  #   gateway4: ***********          # Set your LAN gateway IP
  #   nameservers:
  #     addresses: [***********, *******] # Set your DNS servers