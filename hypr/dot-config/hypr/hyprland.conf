exec-once = nm-applet &
exec-once = waybar & hyprpaper
exec-once=/usr/lib/polkit-gnome/polkit-gnome-authentication-agent-1
# exec-once = dunst
exec-once = swaync
exec-once = hypridle
exec-once = wl-paste --watch cliphist store

monitor=,preferred,auto,1.5

$terminal = kitty
$fileManager = thunar
# $menu = wofi --show drun
# $menu = rofi -terminal $terminal -show drun
$menu = rofi -terminal $terminal -show drun -combi-modes drun#run -modes combi
$menu_window = rofi -terminal $terminal -show window

# env = HYPRCURSOR_SIZE,24
# env = XCURSOR_SIZE,24
# env = CLUTTER_BACKEND,wayland
# env = GDK_BACKEND,wayland,x11
# env = QT_AUTO_SCREEN_SCALE_FACTOR,1.5
# env = QT_QPA_PLATFORM,wayland;xcb
# # env = QT_QPA_PLATFORMTHEME,qt5ct
# env = QT_QPA_PLATFORMTHEME,qt6ct
# env = QT_SCALE_FACTOR,1.5
# # env = QT_WAYLAND_DISABLE_WINDOWDECORATION,1
# env = XDG_CURRENT_DESKTOP,Hyprland
# env = XDG_SESSION_DESKTOP,Hyprland
# env = XDG_SESSION_TYPE,wayland

# env = GDK_SCALE,1.5
# env = MOZ_ENABLE_WAYLAND,1

# electron >28 apps (may help)`
env = ELECTRON_OZONE_PLATFORM_HINT,auto
env = OZONE_PLATFORM,wayland
env = ELECTRON_OZONE_PLATFORM_HINT,wayland

xwayland {
    enabled = false
}

general {
    gaps_in = 5
    gaps_out = 20

    border_size = 2

    col.active_border = rgba(33ccffee) rgba(00ff99ee) 45deg
    col.inactive_border = rgba(595959aa)

    resize_on_border = true

    allow_tearing = false

    layout = dwindle
}

decoration {
    rounding = 10

    active_opacity = 1.0
    inactive_opacity = 1.0

    # shadow {
    #     enabled = true
    #     range = 4
    #     render_power = 3
    #     color = rgba(1a1a1aee)
    # }

    blur {
        # size = 5
        # passes = 3
        # new_optimizations = true
        # ignore_opacity = false

        enabled = true
        size = 3
        passes = 1

        vibrancy = 0.1696
    }
}

animations {
    enabled = yes, please :)

    bezier = easeOutQuint,0.23,1,0.32,1
    bezier = easeInOutCubic,0.65,0.05,0.36,1
    bezier = linear,0,0,1,1
    bezier = almostLinear,0.5,0.5,0.75,1.0
    bezier = quick,0.15,0,0.1,1

    animation = global, 1, 10, default
    animation = border, 1, 5.39, easeOutQuint
    animation = windows, 1, 4.79, easeOutQuint
    animation = windowsIn, 1, 4.1, easeOutQuint, popin 87%
    animation = windowsOut, 1, 1.49, linear, popin 87%
    animation = fadeIn, 1, 1.73, almostLinear
    animation = fadeOut, 1, 1.46, almostLinear
    animation = fade, 1, 3.03, quick
    animation = layers, 1, 3.81, easeOutQuint
    animation = layersIn, 1, 4, easeOutQuint, fade
    animation = layersOut, 1, 1.5, linear, fade
    animation = fadeLayersIn, 1, 1.79, almostLinear
    animation = fadeLayersOut, 1, 1.39, almostLinear
    animation = workspaces, 1, 1.94, almostLinear, fade
    animation = workspacesIn, 1, 1.21, almostLinear, fade
    animation = workspacesOut, 1, 1.94, almostLinear, fade
}

# Ref https://wiki.hyprland.org/Configuring/Workspace-Rules/
# "Smart gaps" / "No gaps when only"
# uncomment all if you wish to use that.
workspace = w[tv1], gapsout:0, gapsin:0
workspace = f[1], gapsout:0, gapsin:0
windowrulev2 = bordersize 0, floating:0, onworkspace:w[tv1]
windowrulev2 = rounding 0, floating:0, onworkspace:w[tv1]
windowrulev2 = bordersize 0, floating:0, onworkspace:f[1]
windowrulev2 = rounding 0, floating:0, onworkspace:f[1]

# Window opacity
windowrulev2 = opacity 0.98, override 1, title:.*
# windowrulev2 = opacity 0.85, override 1, title:Visual Studio Code
# windowrulev2 = opacity 0.95 override 1,class:firefox
# windowrulev2 = opacity 1 override 1,class:steam # Steam fully opaque

# windowrule = tile,^(Brave-browser)$
windowrule = float,^(pavucontrol)$
windowrule = float,^(blueman-manager)$
windowrule = float,^(nm-connection-editor)$

# Browser Picture in Picture
windowrulev2 = float, title:^(Picture-in-Picture)$
windowrulev2 = pin, title:^(Picture-in-Picture)$
windowrulev2 = move 69.5% 4%, title:^(Picture-in-Picture)$

binds {
  workspace_back_and_forth = false
  allow_workspace_cycles = true
  pass_mouse_when_bound = false
}

dwindle {
    # pseudotile = true
    preserve_split = true
}

master {
    new_status = master
}

misc {
    force_default_wallpaper = -1
    disable_hyprland_logo = false
    # disable_hyprland_qtutils_check = true
}

input {
    kb_layout = us
    kb_variant =
    kb_model =
    kb_options = caps:ctrl_modifier
    kb_rules =

    follow_mouse = 1

    sensitivity = 0 # -1.0 - 1.0, 0 means no modification.

    touchpad {
        natural_scroll = false
    }
}

gestures {
    workspace_swipe = false
}

device {
    name = epic-mouse-v1
    sensitivity = -0.5
}


$mainMod = SUPER

bind = $mainMod, RETURN, exec, $terminal
bind = $mainMod, Q, killactive,
bind = $mainMod SHIFT, Q, exit,
bind = $mainMod, E, exec, $fileManager
bind = $mainMod, T, togglefloating,
bind = $mainMod, D, exec, $menu
bind = $mainMod, W, exec, $menu_window
bind = $mainMod, P, pseudo, # dwindle
bind = $mainMod, J, togglesplit, # dwindle

bind = $mainMod, G, togglegroup # Toggle window group
bind = $mainMod, K, swapsplit # Swapsplit

bind = $mainMod, left, movefocus, l
bind = $mainMod, right, movefocus, r
bind = $mainMod, up, movefocus, u
bind = $mainMod, down, movefocus, d

bind = $mainMod ALT, right, resizeactive, 50 0 # Increase window width with keyboard
bind = $mainMod ALT, left, resizeactive, -50 0 # Reduce window width with keyboard
bind = $mainMod ALT, down, resizeactive, 0 50 # Increase window height with keyboard
bind = $mainMod ALT, up, resizeactive, 0 -50 # Reduce window height with keyboard

bindm = $mainMod, mouse:272, movewindow # Move window with the mouse
bindm = $mainMod, mouse:273, resizewindow # Resize window with the mouse

# Switch workspaces with mainMod + [0-9]
bind = $mainMod, 1, workspace, 1
bind = $mainMod, 2, workspace, 2
bind = $mainMod, 3, workspace, 3
bind = $mainMod, 4, workspace, 4
bind = $mainMod, 5, workspace, 5
bind = $mainMod, 6, workspace, 6
bind = $mainMod, 7, workspace, 7
bind = $mainMod, 8, workspace, 8
bind = $mainMod, 9, workspace, 9
bind = $mainMod, 0, workspace, 10

# Move active window to a workspace with mainMod + SHIFT + [0-9]
bind = $mainMod SHIFT, 1, movetoworkspace, 1
bind = $mainMod SHIFT, 2, movetoworkspace, 2
bind = $mainMod SHIFT, 3, movetoworkspace, 3
bind = $mainMod SHIFT, 4, movetoworkspace, 4
bind = $mainMod SHIFT, 5, movetoworkspace, 5
bind = $mainMod SHIFT, 6, movetoworkspace, 6
bind = $mainMod SHIFT, 7, movetoworkspace, 7
bind = $mainMod SHIFT, 8, movetoworkspace, 8
bind = $mainMod SHIFT, 9, movetoworkspace, 9
bind = $mainMod SHIFT, 0, movetoworkspace, 10

bind = $mainMod, Tab, workspace, m+1 # Open next workspace
bind = $mainMod SHIFT, Tab, workspace, m-1 # Open previous workspace

bind = $mainMod, mouse_down, workspace, e+1 # Open next workspace
bind = $mainMod, mouse_up, workspace, e-1 # Open previous workspace
bind = $mainMod CTRL, down, workspace, empty # Open the next empty workspace


# Example special workspace (scratchpad)
bind = $mainMod, S, togglespecialworkspace, magic
bind = $mainMod SHIFT, S, movetoworkspace, special:magic

# Scroll through existing workspaces with mainMod + scroll
bind = $mainMod, mouse_down, workspace, e+1
bind = $mainMod, mouse_up, workspace, e-1


# Dwindle Layout
# bind = $mainMod, P, pseudo, # dwindle

# Master Layout
bind = $mainMod CTRL, D, layoutmsg, removemaster
bind = $mainMod, I, layoutmsg, addmaster
bind = $mainMod, J, layoutmsg, cyclenext
bind = $mainMod, K, layoutmsg, cycleprev
bind = $mainMod CTRL, Return, layoutmsg, swapwithmaster

 # Cycle windows if floating bring to top
bind = ALT, tab, cyclenext
bind = ALT, tab, bringactivetotop

bind = $mainMod SHIFT, left,  swapwindow, l
bind = $mainMod SHIFT, down,  swapwindow, d
bind = $mainMod SHIFT, up,    swapwindow, u
bind = $mainMod SHIFT, right, swapwindow, r

bind = $mainMod SHIFT, h,  movewindow, l
bind = $mainMod SHIFT, j,  movewindow, d
bind = $mainMod SHIFT, k,  movewindow, u
bind = $mainMod SHIFT, l,  movewindow, r

bind = $mainMod CTRL, left, workspace,  e-1
bind = $mainMod CTRL, right, workspace, e+1

bind = $mainMod SHIFT CTRL, left, movetoworkspace,  r-1
bind = $mainMod SHIFT CTRL, right, movetoworkspace, r+1

bind = $mainMod ALT, s, layoutmsg, swapwithmaster master
bind = $mainMod ALT, equal, layoutmsg, addmaster
bind = $mainMod ALT, minus, layoutmsg, removemaster
bind = $mainMod ALT, l, layoutmsg, orientationleft
bind = $mainMod ALT, r, layoutmsg, orientationright
bind = $mainMod ALT, c, layoutmsg, orientationcenter

# bind = $mainMod ALT, right, layoutmsg, rollnext
# bind = $mainMod ALT, left, layoutmsg, rollprev

# Move/resize windows with mainMod + LMB/RMB and dragging
bindm = $mainMod, mouse:272, movewindow
bindm = $mainMod, mouse:273, resizewindow

# Laptop multimedia keys for volume and LCD brightness
bindel = ,XF86AudioRaiseVolume, exec, wpctl set-volume @DEFAULT_AUDIO_SINK@ 5%+
bindel = ,XF86AudioLowerVolume, exec, wpctl set-volume @DEFAULT_AUDIO_SINK@ 5%-
bindel = ,XF86AudioMute, exec, wpctl set-mute @DEFAULT_AUDIO_SINK@ toggle
bindel = ,XF86AudioMicMute, exec, wpctl set-mute @DEFAULT_AUDIO_SOURCE@ toggle
bindel = ,XF86MonBrightnessUp, exec, brightnessctl s 10%+
bindel = ,XF86MonBrightnessDown, exec, brightnessctl s 10%-

# Requires playerctl
bindl = , XF86AudioNext, exec, playerctl next
bindl = , XF86AudioPause, exec, playerctl play-pause
bindl = , XF86AudioPlay, exec, playerctl play-pause
bindl = , XF86AudioPrev, exec, playerctl previous

# bind = $mainMod SHIFT, A, exec, $HYPRSCRIPTS/toggle-animations.sh # Toggle animations
# bind = $mainMod, PRINT, exec, $HYPRSCRIPTS/screenshot.sh # Take a screenshot
# bind = $mainMod SHIFT, S, exec, $HYPRSCRIPTS/screenshot.sh # Take a screenshot
# bind = $mainMod CTRL, Q, exec, ~/.config/ml4w/scripts/wlogout.sh # Start wlogout
# bind = $mainMod SHIFT, W, exec, waypaper --random # Change the wallpaper
# bind = $mainMod CTRL, W, exec, waypaper # Open wallpaper selector
# bind = $mainMod ALT, W, exec, $HYPRSCRIPTS/wallpaper-automation.sh # Start random wallpaper script
# bind = $mainMod CTRL, RETURN, exec, pkill rofi || rofi -show drun -replace -i # Open application launcher
# bind = $mainMod CTRL, K, exec, $HYPRSCRIPTS/keybindings.sh # Show keybindings
# bind = $mainMod SHIFT, B, exec, ~/.config/waybar/launch.sh # Reload waybar
# bind = $mainMod CTRL, B, exec, ~/.config/waybar/toggle.sh # Toggle waybar
# bind = $mainMod SHIFT, R, exec, $HYPRSCRIPTS/loadconfig.sh # Reload hyprland config
# bind = $mainMod, V, exec, $SCRIPTS/cliphist.sh # Open clipboard manager
# bind = $mainMod CTRL, T, exec, ~/.config/waybar/themeswitcher.sh # Open waybar theme switcher
# bind = $mainMod CTRL, S, exec, ~/.config/ml4w/apps/ML4W_Dotfiles_Settings-x86_64.AppImage # Open ML4W Dotfiles Settings app
# bind = $mainMod SHIFT, H, exec, $HYPRSCRIPTS/hyprshade.sh # Toggle screenshader
# bind = $mainMod ALT, G, exec, $HYPRSCRIPTS/gamemode.sh # Toggle game mode
# bind = $mainMod, Z, exec, missioncenter # Open Mission Center

# Ignore maximize requests from apps. You'll probably like this.
windowrulev2 = suppressevent maximize, class:.*

# Fix some dragging issues with XWayland
windowrulev2 = nofocus,class:^$,title:^$,xwayland:1,floating:1,fullscreen:0,pinned:0

xwayland {
    force_zero_scaling = true
}

exec-once=dbus-update-activation-environment --systemd WAYLAND_DISPLAY XDG_CURRENT_DESKTOP
exec-once=/usr/libexec/lxqt-policykit-agent
