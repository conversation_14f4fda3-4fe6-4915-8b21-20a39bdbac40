#  _                      _     _ _
# | |__  _   _ _ __  _ __(_) __| | | ___
# | '_ \| | | | '_ \| '__| |/ _` | |/ _ \
# | | | | |_| | |_) | |  | | (_| | |  __/
# |_| |_|\__, | .__/|_|  |_|\__,_|_|\___|
#        |___/|_|
#
# Version 2.8.3
# DO NOT REMOVE THE REPLACEMENT COMMENTS
# REQUIRED BY THE ML4W SETTINGS APP
# https://wiki.hyprland.org/Hypr-Ecosystem/hypridle/

# general {
#     ignore_dbus_inhibit = false
# }

general {
    lock_cmd = pidof hyprlock || hyprlock       # avoid starting multiple hyprlock instances.
    before_sleep_cmd = loginctl lock-session    # lock before suspend.
    after_sleep_cmd = hyprctl dispatch dpms on  # to avoid having to press a key twice to turn on the display.
}

# Screenlock
listener {
    # HYPRLOCK TIMEOUT
    timeout = 180
    # HYPRLOCK ONTIMEOUT
    on-timeout = loginctl lock-session
}

# dpms
listener {
    # DPMS TIMEOUT
    timeout = 210
    # DPMS ONTIMEOUT
    on-timeout = hyprctl dispatch dpms off
    # DPMS ONRESUME
    on-resume = hyprctl dispatch dpms on
}

# Suspend
listener {
    # SUSPEND TIMEOUT
    timeout = 900
    #SUSPEND ONTIMEOUT
    on-timeout = systemctl suspend
}
