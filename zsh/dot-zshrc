# shellcheck disable=SC2148
export XDG_CONFIG_HOME=${XDG_CONFIG_HOME:-$HOME/.config}
export DOT_DIR=${DOT_DIR:-$HOME/.ilm}

[[ -d "$DOT_DIR" ]] || return

# shellcheck disable=SC1091
source "$DOT_DIR/share/shellrc"

# if interactive shell then return
[[ $- == *i* ]] || return

HISTFILE=~/.histfile
HISTSIZE=1000
# shellcheck disable=SC2034
SAVEHIST=1000
bindkey -e
zstyle :compinstall filename "$HOME/.zshrc"

setopt AUTO_CD
setopt AUTO_PUSHD
setopt PUSHD_IGNORE_DUPS
setopt PUSHD_SILENT
setopt PUSHD_TO_HOME
setopt EXTENDED_GLOB
setopt AUTO_MENU            # Show completion menu on a successive tab press.
setopt AUTO_LIST            # Automatically list choices on ambiguous completion.
setopt prompt_subst

autoload bashcompinit && bashcompinit

autoload -Uz compinit
compinit

export CARAPACE_BRIDGES='zsh,fish,bash,inshellisense' # optional
zstyle ':completion:::::default' menu select
# zstyle ':completion:*' format $'\e[2;37mCompleting %d\e[m'

if has_cmd starship; then
    eval "$(starship init zsh)"
else
    if ! [[ -d $HOME/.zsh/pure ]]; then
        if  has_cmd git > /dev/null; then
            git clone --depth=1 https://github.com/sindresorhus/pure.git "$HOME/.zsh/pure"
        else
            echo "git not installed"
        fi
    fi

    fpath+=("$HOME"/.zsh/pure)
    autoload -U promptinit; promptinit
    prompt pure
fi

# initialise completions with ZSH's compinit
autoload -Uz compinit && compinit

# has_cmd direnv && eval "$(direnv hook zsh)"
# shellcheck disable=SC1090
has_cmd carapace && source <(carapace _carapace)
has_cmd ~/.local/bin/mise && eval "$(~/.local/bin/mise activate zsh)"
has_cmd zoxide && eval "$(zoxide init zsh)"
# shellcheck disable=SC1090
has_cmd fzf && source <(fzf --zsh)
has_cmd atuin && eval "$(atuin init zsh --disable-up-arrow --disable-ctrl-r)"
has_cmd ~/.pixi/bin/pixi && eval "$(~/.pixi/bin/pixi completion --shell zsh)"

source_if_exists /etc/zsh_command_not_found

if [[ -f /usr/share/zsh-syntax-highlighting/zsh-syntax-highlighting.zsh ]]; then
    source_if_exists /usr/share/zsh-syntax-highlighting/zsh-syntax-highlighting.zsh
else
    if ! [[ -d $HOME/.zsh/zsh-syntax-highlighting ]]; then
        has_cmd git && git clone --depth=1 https://github.com/zsh-users/zsh-syntax-highlighting.git "$HOME/.zsh/zsh-syntax-highlighting"
    fi
    source_if_exists "$HOME/.zsh/zsh-syntax-highlighting/zsh-syntax-highlighting.zsh"
fi

if [[ -f /usr/share/zsh-autosuggestions/zsh-autosuggestions.zsh ]]; then
    source_if_exists /usr/share/zsh-autosuggestions/zsh-autosuggestions.zsh
else
    if ! [[ -d $HOME/.zsh/zsh-autosuggestions ]]; then
        has_cmd git && git clone --depth=1 https://github.com/zsh-users/zsh-autosuggestions "$HOME/.zsh/zsh-autosuggestions"
    fi
    source_if_exists "$HOME/.zsh/zsh-autosuggestions/zsh-autosuggestions.zsh"
fi

if ! [[ -d $HOME/.zsh/alias-tips ]]; then
    has_cmd git && git clone --depth=1 https://github.com/djui/alias-tips.git "$HOME/.zsh/alias-tips"
fi
source_if_exists "$HOME/.zsh/zsh-autosuggestions/alias-tips/alias-tips.plugin.zsh"

# Copied from oh-my-zsh

# http://zsh.sourceforge.net/Doc/Release/Zsh-Line-Editor.html
# http://zsh.sourceforge.net/Doc/Release/Zsh-Line-Editor.html#Zle-Builtins
# http://zsh.sourceforge.net/Doc/Release/Zsh-Line-Editor.html#Standard-Widgets

# Make sure that the terminal is in application mode when zle is active, since
# only then values from $terminfo are valid
if (( ${+terminfo[smkx]} )) && (( ${+terminfo[rmkx]} )); then
    function zle-line-init() {
    echoti smkx
    }
    function zle-line-finish() {
    echoti rmkx
    }
    zle -N zle-line-init
    zle -N zle-line-finish
fi

# [PageUp] - Up a line of history
if [[ -n "${terminfo[kpp]}" ]]; then
    bindkey -M emacs "${terminfo[kpp]}" up-line-or-history
fi
# [PageDown] - Down a line of history
if [[ -n "${terminfo[knp]}" ]]; then
    bindkey -M emacs "${terminfo[knp]}" down-line-or-history
fi

# Start typing + [Up-Arrow] - fuzzy find history forward
if [[ -n "${terminfo[kcuu1]}" ]]; then
    autoload -U up-line-or-beginning-search
    zle -N up-line-or-beginning-search

    bindkey -M emacs "${terminfo[kcuu1]}" up-line-or-beginning-search
fi
# Start typing + [Down-Arrow] - fuzzy find history backward
if [[ -n "${terminfo[kcud1]}" ]]; then
    autoload -U down-line-or-beginning-search
    zle -N down-line-or-beginning-search

bindkey -M emacs "${terminfo[kcud1]}" down-line-or-beginning-search
fi

# [Home] - Go to beginning of line
if [[ -n "${terminfo[khome]}" ]]; then
    bindkey -M emacs "${terminfo[khome]}" beginning-of-line
fi
# [End] - Go to end of line
if [[ -n "${terminfo[kend]}" ]]; then
    bindkey -M emacs "${terminfo[kend]}" end-of-line
fi

# [Shift-Tab] - move through the completion menu backwards
if [[ -n "${terminfo[kcbt]}" ]]; then
    bindkey -M emacs "${terminfo[kcbt]}" reverse-menu-complete
fi

# [Backspace] - delete backward
bindkey -M emacs '^?' backward-delete-char
# [Delete] - delete forward
if [[ -n "${terminfo[kdch1]}" ]]; then
    bindkey -M emacs "${terminfo[kdch1]}" delete-char
else
    bindkey -M emacs "^[[3~" delete-char

    bindkey -M emacs "^[3;5~" delete-char
fi

# [Ctrl-Delete] - delete whole forward-word
bindkey -M emacs '^[[3;5~' kill-word

# [Ctrl-RightArrow] - move forward one word
bindkey -M emacs '^[[1;5C' forward-word
# [Ctrl-LeftArrow] - move backward one word
bindkey -M emacs '^[[1;5D' backward-word

bindkey '\ew' kill-region                             # [Esc-w] - Kill from the cursor to the mark
bindkey -s '\el' 'ls\n'                               # [Esc-l] - run command: ls
# bindkey '^r' history-incremental-search-backward      # [Ctrl-r] - Search backward incrementally for a specified string. The string may begin with ^ to anchor the search to the beginning of the line.
bindkey ' ' magic-space                               # [Space] - don't do history expansion

# Edit the current command line in $EDITOR
autoload -U edit-command-line
zle -N edit-command-line
bindkey '\C-x\C-e' edit-command-line

# file rename magick
bindkey "^[m" copy-prev-shell-word

bindkey '^[[A' up-line-or-search
bindkey '^[[B' down-line-or-search
bindkey '^[^[[C' emacs-forward-word
bindkey '^[^[[D' emacs-backward-word

bindkey -s '^X^Z' '%-^M'
bindkey '^[e' expand-cmd-path
bindkey '^[^I' reverse-menu-complete
bindkey '^X^N' accept-and-infer-next-history
bindkey '^W' kill-region
bindkey '^I' complete-word

## Fix weird sequence that rxvt produces
#bindkey -s '^[[Z' '\t'
#

bindkey '^w' autosuggest-execute
bindkey '^e' autosuggest-accept
bindkey '^u' autosuggest-toggle
bindkey '^L' vi-forward-word
bindkey '^k' up-line-or-search
bindkey '^j' down-line-or-search

source_if_exists "$DOT_DIR/share/fns"
source_if_exists "$DOT_DIR/share/aliases"

# Called before prompt(?)
function precmd {
    # Set window title
    print -Pn "\e]0;zsh%L %(1j,%j job%(2j|s|); ,)%~\e\\"
}

# Called when executing a command
function preexec {
    print -Pn "\e]0;${1:q}\e\\"
}

source_if_exists "$HOME/.localzshrc"

# is_wsl && is_tw && sudo systemd-tmpfiles --create

# one_shell_tmux

source_if_exists ~/.config/broot/launcher/bash/br
