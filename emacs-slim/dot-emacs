(setq package-enable-at-startup nil)

(defconst *is-a-mac* (eq system-type 'darwin))
(defconst *is-a-linux* (eq system-type 'gnu/linux))
(defconst *is-a-windows* (eq system-type 'windows-nt))

(defvar bootstrap-version)
(let ((bootstrap-file
       (expand-file-name
        "straight/repos/straight.el/bootstrap.el"
        (or (bound-and-true-p straight-base-dir)
            user-emacs-directory)))
      (bootstrap-version 7))
  (unless (file-exists-p bootstrap-file)
    (with-current-buffer
        (url-retrieve-synchronously
         "https://raw.githubusercontent.com/radian-software/straight.el/develop/install.el"
         'silent 'inhibit-cookies)
      (goto-char (point-max))
      (eval-print-last-sexp)))
  (load bootstrap-file nil 'nomessage))

(setq straight-use-package-by-default t)
(straight-use-package 'use-package)

(use-package catppuccin-theme
  :config
  (load-theme 'catppuccin t))

;; (require 'use-package)
;; (setq use-package-always-ensure t)
;; (setq use-package-expand-minimally t)

;; (use-package package
;;   :custom
;;   (package-archives '(("melpa" . "https://melpa.org/packages/")
;;                       ("org" . "https://orgmode.org/elpa/")
;;                       ("elpa" . "https://elpa.gnu.org/packages/")))
;;   :config
;;   (package-initialize)
;;   (unless (package-installed-p 'use-package)
;;     (package-refresh-contents)
;;     (package-install 'use-package))
;;   (setq use-package-always-ensure t))

(use-package emacs
  :init
  ;; (load-theme 'wombat t)

  (when (display-graphic-p)
    (if (find-font (font-spec :name "JetbrainsMono Nerd Font"))
        (set-face-attribute 'default nil
                            :family "JetBrainsMono Nerd Font"
                            :height 120)))
  (when *is-a-mac*
    (setq mac-option-modifier 'meta)
    (custom-set-variables '(ns-use-srgb-colorspace nil)))

  (when *is-a-windows*
    (setq default-directory "~/")

    (setq inhibit-compacting-font-caches t)

    (setq w32-pass-lwindow-to-system nil)
    (setq w32-lwindow-modifier 'super)

    (setq w32-pass-rwindow-to-system nil)
    (setq w32-rwindow-modifier 'super)

    (setq w32-pass-apps-to-system nil)
    (setq w32-apps-modifier 'hyper))


  (setq custom-file (expand-file-name "custom.el" user-emacs-directory))
  ;; Ensure custom.el doesn't get loaded even if it exists
  (when (file-exists-p custom-file)
    (delete-file custom-file))

  (setq-default show-trailing-whitespace t)
  (add-hook 'before-save-hook 'delete-trailing-whitespace)

  ;; do not ask follow link
  (customize-set-variable 'find-file-visit-truename t)
  (setq vc-follow-symlinks t)

  (fset 'yes-or-no-p 'y-or-n-p)

  (setq gc-cons-threshold 100000000)
  (setq read-process-output-max (* 1024 1024))
  (setq inhibit-compacting-font-caches t)

  (unless (display-graphic-p)
    (set-face-background 'default "unspecified-bg"))

  (setq initial-major-mode 'text-mode)

  (setq locale-coding-system 'utf-8)
  (set-terminal-coding-system 'utf-8)
  (set-keyboard-coding-system 'utf-8)
  (set-selection-coding-system 'utf-8)
  (prefer-coding-system 'utf-8)

  (setq create-lockfiles nil)
  (setq inhibit-startup-message t)
  (setq initial-scratch-message nil)
  (setq-default indent-tabs-mode nil)
  (setq-default tab-width 4)
  (setq ring-bell-function 'ignore)

  ;; allow system clipboard
  (setq select-enable-primary nil)
  (setq select-enable-clipboard t)
  (setq x-select-enable-primary nil)
  (setq x-select-enable-clipboard t)
  (setq xclip-select-enable-clipboard t)

  (setq require-final-newline t)
  (setq load-prefer-newer t)

  :config
  (setq frame-inhibit-implied-resize t)
  (setq fast-but-imprecise-scrolling t)
  (setq redisplay-skip-fontification-on-input t)

  (if (fboundp 'tool-bar-mode)
      (tool-bar-mode -1))
  (if (fboundp 'set-scroll-bar-mode)
      (set-scroll-bar-mode nil))
  (if (fboundp 'menu-bar-mode)
      (menu-bar-mode -1))
  (setq use-dialog-box nil)
  (setq use-file-dialog nil)

  (fido-mode 1)
  (fido-vertical-mode 1)

  (repeat-mode 1)
  (column-number-mode 1)
  (size-indication-mode 1)
  (global-visual-line-mode 1)
  (show-paren-mode 1)
  (delete-selection-mode 1)
  (electric-pair-mode 1)
  (pixel-scroll-precision-mode 1)

  :custom
  (cursor-type 'box)
  (blink-cursor-mode nil)

  (delete-by-moving-to-trash t)
  (confirm-kill-processes nil)
  (create-lockfiles nil)
  (auto-save-default nil)
  (make-backup-files nil)
  (create-lockfiles nil)
  (make-backup-files nil)
  (auto-save-default nil)
  (scroll-conservatively 100)
  (use-short-answers t))

(use-package mouse
  :straight nil
  :config
  (setq mouse-drag-and-drop-region t
        mouse-drag-and-drop-region-cut-when-buffers-differ t
        mouse-drag-and-drop-region-show-tooltip t
        mouse-drag-and-drop-region-show-cursor t)

  (unless (display-graphic-p)
    (require 'mouse)
    (xterm-mouse-mode 1)
    (global-set-key [mouse-4] (lambda () (interactive) (scroll-down 5)))
    (global-set-key [mouse-5] (lambda () (interactive) (scroll-up 5)))))

(use-package uniquify
  :straight nil
  :init
  (setq uniquify-buffer-name-style 'forward
        uniquify-separator "/"
        uniquify-after-kill-buffer-p t
        uniquify-ignore-buffers-re "^\\*"
        uniquify-min-dir-content 1))

(use-package blackout
  :ensure t
  :config
  (blackout 'auto-fill-mode)
  (blackout 'flymake-mode)
  (blackout 'auto-revert-mode)
  (blackout 'word-wrap-whitespace-mode)
  (blackout 'visual-line-mode)
  (blackout 'hs-minor-mode)
  (blackout 'auto-revert-mode))

(use-package xdg
  :ensure t
  :config
  (setq user-emacs-directory (expand-file-name "emacs/" (xdg-config-home)))
  ;;  (setq custom-file (expand-file-name "custom.el" user-emacs-directory))

  (setq backup-directory-alist `(("." . ,(expand-file-name "emacs/backups" (xdg-state-home)))))
  (setq auto-save-file-name-transforms `((".*" ,(expand-file-name "emacs/autosaves/" (xdg-cache-home)) t)))
  (setq auto-save-list-file-prefix (expand-file-name "emacs/auto-save-list/saves-" (xdg-cache-home)))
  (setq url-history-file (expand-file-name "emacs/url/history" (xdg-state-home)))

  (setq bookmark-default-file (expand-file-name "emacs/bookmarks" (xdg-data-home)))
  (setq recentf-save-file (expand-file-name "emacs/recentf" (xdg-state-home)))
  (setq auto-save-list-file-prefix (expand-file-name "emacs/auto-save-list/saves-" (xdg-cache-home)))
  (setq auto-save-list-file-prefix (expand-file-name "emacs/auto-save-list/saves-" (xdg-cache-home)))

  (setq desktop-dirname (expand-file-name "emacs/desktop/" (xdg-state-home)))
  (setq desktop-path (list desktop-dirname))
  (setq desktop-base-file-name "emacs.desktop")

  (setq org-directory (expand-file-name "emacs/org/" (xdg-data-home)))
  (setq tramp-persistency-file-name (expand-file-name "emacs/tramp" (xdg-state-home))))

(use-package recentf
  :straight nil
  :config
  (recentf-mode 1)
  :custom
  (recentf-max-saved-items 100)
  (recentf-max-menu-items 15)
  (recentf-auto-cleanup 'never)
  (recentf-exclude '("/tmp/" "/ssh:"))
  (recentf-save-file (locate-user-emacs-file "recentf"))
  :bind ("C-x C-r" . recentf-open-files))

(use-package savehist
  :straight nil
  :config
  (savehist-mode 1)
  :custom
  (savehist-additional-variables '(search-ring regexp-search-ring))
  (savehist-autosave-interval 60))

(use-package server
  :straight nil
  :config
  (unless (server-running-p)
    (server-start)))

(use-package saveplace
  :straight nil
  :config
  (save-place-mode 1)
  :custom
  (save-place-file (locate-user-emacs-file "places")))

(use-package savehist
  :straight nil
  :init
  (setq desktop-save 'if-exists)
  :config
  (savehist-mode 1)
  :custom
  (savehist-file (locate-user-emacs-file "history"))
  (savehist-additional-variables '(search-ring regexp-search-ring))
  (savehist-autosave-interval 60))

(use-package desktop
  :straight nil
  :config
  (desktop-save-mode 1)
  :custom
  (desktop-path (list user-emacs-directory))
  (desktop-auto-save-timeout 30)
  (desktop-restore-eager 5)
  (desktop-load-locked-desktop t))

(use-package xclip
  :config
  (xclip-mode +1))

(use-package sudo-edit
  :bind (("C-c C-r" . sudo-edit)
         ("C-c C-f" . sudo-edit-find-file))
  :config
  (setq sudo-edit-indicator-mode t
        sudo-edit-local-method "sudo")

  (defun my/sudo-edit-on-save-error ()
    "Automatically reopen file with sudo if save fails due to permissions."
    (interactive)
    (when (and buffer-file-name
               (not (file-writable-p buffer-file-name)))
      (when (y-or-n-p "File not writable. Reopen it as root? ")
        (sudo-edit))))

  (add-hook 'find-file-hook #'my/sudo-edit-on-save-error))

(use-package ace-window
  :bind ("C-x o" . ace-window)
  :init
  (setq aw-dispatch-always t))

(winner-mode)
(windmove-default-keybindings)

(use-package buffer-move
  :bind (("<C-S-up>" . buf-move-up)
         ("<C-S-down>" . buf-move-down)
         ("<C-S-left>" . buf-move-left)
         ("<C-S-right>" . buf-move-right)))

(bind-key [remap just-one-space] 'cycle-spacing)
(bind-key "RET" 'newline-and-indent)

(use-package move-dup
  :bind (("M-S-<down>" . move-dup-move-lines-down)
         ("M-<down>" . move-dup-duplicate-down)
         ("M-<up>" . move-dup-duplicate-up)
         ("M-S-<up>" . move-dup-move-lines-up)))

(use-package magit
  :ensure t
  :bind (("C-x g" . magit-status))
  :custom
  (magit-display-buffer-function #'magit-display-buffer-same-window-except-diff-v1)
  (magit-diff-refine-hunk 'all)
  (magit-save-repository-buffers 'dontask)
  ;; (magit-repository-directories '(("~/programs" . 2) ("~/.ilm" . 1)))  ;; Define Git repos
  :config
  ;; Improve Magit performance on large repositories
  (setq magit-refresh-status-buffer nil)

  ;; Automatically Refresh Magit Buffers**
  (add-hook 'after-save-hook #'magit-after-save-refresh-status)

  (with-eval-after-load 'project
    (define-key project-prefix-map (kbd "m") #'magit-project-status)))

;; (use-package sqlite3) ;; forge needs sqlite3

;; GitHub/GitLab Issues & PRs**
;; (use-package forge
;;   :after magit
;;   :config
;;   (setq forge-alist nil)
;;   :custom
;;   (forge-topic-list-limit '(100 . -10))  ;; Show last 100 issues & PRs
;;   (auth-sources '("~/.authinfo.gpg")))  ;; Store credentials securely

(use-package which-key
  :init
  (which-key-mode))

(use-package wgrep
  :custom
  (wgrep-auto-save-buffer t)
  (wgrep-change-readonly-file t))

(use-package rainbow-mode
  :blackout rainbow-mode
  :hook org-mode prog-mode)

(use-package rainbow-delimiters
  :hook ((emacs-lisp-mode . rainbow-delimiters-mode)
         (clojure-mode . rainbow-delimiters-mode)))

(use-package treesit
  :straight nil
  :config
  (setq treesit-language-source-alist
        '((python "https://github.com/tree-sitter/tree-sitter-python")
          (rust "https://github.com/tree-sitter/tree-sitter-rust")
          (c "https://github.com/tree-sitter/tree-sitter-c")
          (ocaml "https://github.com/tree-sitter/tree-sitter-ocaml")
          (go "https://github.com/tree-sitter/tree-sitter-go")
          (html "https://github.com/tree-sitter/tree-sitter-html")
          (css "https://github.com/tree-sitter/tree-sitter-css")
          (java "https://github.com/tree-sitter/tree-sitter-java")
          (haskell "https://github.com/tree-sitter/tree-sitter-haskell")
          (scala "https://github.com/tree-sitter/tree-sitter-scala")
          (toml "https://github.com/tree-sitter/tree-sitter-toml")
          (json "https://github.com/tree-sitter/tree-sitter-json")
          (javascript "https://github.com/tree-sitter/tree-sitter-javascript")
          (typescript "https://github.com/tree-sitter/tree-sitter-typescript")
          (bash "https://github.com/tree-sitter/tree-sitter-bash")
          (cpp "https://github.com/tree-sitter/tree-sitter-cpp")))

  (dolist (lang '(c cpp python json toml bash))
    (unless (treesit-language-available-p lang)
      (treesit-install-language-grammar lang)))

  (dolist (lang '(python rust c c++ json toml bash))
    (add-to-list 'major-mode-remap-alist
                 (cons (intern (format "%s-mode" lang))
                       (intern (format "%s-ts-mode" lang))))))

(use-package corfu
  :ensure t
  :custom
  (corfu-cycle t)                ;; Enable cycling for completions
  (corfu-auto t)                 ;; Enable auto completion
  (corfu-auto-delay 0.2)
  (corfu-preselect 'first)       ;; Preselect first candidate
  (corfu-quit-at-boundary nil)   ;; Keep completion popup at boundary
  (corfu-quit-no-match 'separator)
  (corfu-scroll-margin 5)
  :init
  (global-corfu-mode)
  :bind
  (:map corfu-map
        ("M-d" . corfu-popupinfo--toggle))
  :config
  (corfu-popupinfo-mode 1))

(use-package corfu-terminal
  :config
  (unless (display-graphic-p)
    (corfu-terminal-mode +1)))

(use-package kind-icon
  :ensure t
  :config
  (add-hook 'my-completion-ui-mode-hook
   	        (lambda ()
   	          (setq completion-in-region-function
   		            (kind-icon-enhance-completion
   		             completion-in-region-function)))))

(use-package cape
  :ensure t
  :init
  ;; Useful completion sources
  (add-to-list 'completion-at-point-functions #'cape-file)
  (add-to-list 'completion-at-point-functions #'cape-dabbrev)
  (add-to-list 'completion-at-point-functions #'cape-keyword))

(use-package orderless
  :custom
  (completion-styles '(orderless))
  (completion-category-defaults nil)
  (completion-category-overrides '((eglot (styles orderless)))))

(use-package eglot
  :straight nil

  :hook ((c-mode c++-mode c-ts-mode c++-ts-mode python-mode python-ts-mode rust-mode rust-ts-mode) . eglot-ensure)

  :config
  (with-eval-after-load 'eglot
    (add-to-list 'eglot-server-programs
                 '(c++-mode . ("clangd"
                               "-j=8"
                               ;; "-std=c++23"
                               ;; "-stdlib=c++"
                               "--log=error"
                               "--malloc-trim"
                               "--background-index"
                               "--clang-tidy"
                               "--completion-style=detailed"
                               "--pch-storage=memory"
                               "--header-insertion=never"
                               "--header-insertion-decorators=0")))))

;; Enable LLDB Debugging in Emacs
(use-package realgud-lldb
  :ensure t
  :commands (realgud-lldb)
  :config
  (setq realgud:lldb-command-name "lldb"))

(use-package apheleia
  :ensure t
  :config
  (setq apheleia-formatters
        (append apheleia-formatters
                '((clang-format . ("clang-format" "-style=file")))))

  (setf (alist-get 'ruff apheleia-formatters)
        '("ruff" "format" "-"))

  (setf (alist-get 'python-mode apheleia-mode-alist) 'ruff)

  (apheleia-global-mode +1))

(use-package isearch
  :straight nil
  :custom
  (isearch-allow-scroll t)
  (isearch-lazy-highlight t)
  (isearch-lazy-count t)
  (lazy-highlight-initial-delay 0)
  (lazy-highlight-cleanup t)
  (search-highlight t)
  (search-whitespace-regexp ".*?")
  (case-fold-search t)                    ;; Case-insensitive unless uppercase used
  (isearch-regexp-lax-whitespace nil)     ;; Don't treat spaces as regex by default
  (isearch-lax-whitespace t)
  (isearch-regexp-lax-whitespace nil)
  :config
  (define-key isearch-mode-map [remap isearch-delete-char] 'isearch-del-char)
  (define-key isearch-mode-map "\C-h" 'isearch-delete-char)

  (define-key isearch-mode-map (kbd "<return>") 'isearch-repeat-forward)
  (define-key isearch-mode-map (kbd "S-<return>") 'isearch-repeat-backward)

  ;; 🔄 Regex toggle and yanking improvements
  (define-key isearch-mode-map (kbd "M-r") 'isearch-toggle-regexp)
  (define-key isearch-mode-map (kbd "M-s C-e") 'isearch-yank-line)
  (define-key isearch-mode-map (kbd "M-s w") 'isearch-yank-word)

  ;; ⚡ Recenter after search end
  (add-hook 'isearch-mode-end-hook #'(lambda () (recenter))))

(use-package eldoc
  :straight nil
  :blackout eldoc-mode
  :init
  (add-hook 'eval-expression-minibuffer-setup-hook #'eldoc-mode)
  (add-hook 'ielm-mode-hook #'eldoc-mode)
  (add-hook 'emacs-lisp-mode-hook #'eldoc-mode)
  :custom
  (eldoc-idle-delay 0.2))

(use-package dired
  :straight nil
  :config
  (setq dired-recursive-copies 'always
        dired-recursive-deletes 'always
        dired-dwim-target t
        delete-by-moving-to-trash t)
  (put 'dired-find-alternate-file 'disabled nil)
  :hook
  (dired-mode . dired-hide-details-mode)
  :custom
  (dired-listing-switches "-alh" --group-directories-first)
  (dired-recursive-deletes 'always)
  (dired-recursive-copies 'always))

(use-package ediff
  :straight nil
  :init
  (setq  ediff-window-setup-function 'ediff-setup-windows-plain
         ediff-split-window-function 'split-window-horizontally
         ediff-merge-split-window-function 'split-window-horizontally))

(use-package grep
  :straight nil
  :config
  (when (eq system-type 'windows-nt)
    (setq grep-use-null-device t))
  :custom
  (grep-command "grep --color=auto -nHi -e -r --exclude-dir={.git,node_modules} ")
  (grep-find-command '("find . -type f -exec grep --color=auto -nHi -e  {} +" . 34))
  (grep-highlight-matches t)
  (grep-scroll-output t))

(use-package json
  :straight nil
  :mode "\\.json\\'"
  :custom
  (json-reformat:pretty-string? t)
  (json-serialize-pretty t)
  (json-object-type 'plist))

(use-package flycheck :ensure t)
