(setq package-enable-at-startup nil)

(defconst *is-a-mac* (eq system-type 'darwin))
(defconst *is-a-linux* (eq system-type 'gnu/linux))
(defconst *is-a-windows* (eq system-type 'windows-nt))

(load-theme 'wombat t)

(load-theme 'wombat t)

(when (display-graphic-p)
  (if (find-font (font-spec :name "JetbrainsMono Nerd Font"))
      (set-face-attribute 'default nil
                          :family "JetBrainsMono Nerd Font"
                          :weight 'bold
                          :height 120)))
(when *is-a-mac*
  (setq mac-option-modifier 'meta)
  (custom-set-variables '(ns-use-srgb-colorspace nil)))

(when *is-a-windows*
  (setq default-directory "~/")

  (setq inhibit-compacting-font-caches t)

  (setq w32-pass-lwindow-to-system nil)
  (setq w32-lwindow-modifier 'super)

  (setq w32-pass-rwindow-to-system nil)
  (setq w32-rwindow-modifier 'super)

  (setq w32-pass-apps-to-system nil)
  (setq w32-apps-modifier 'hyper))


(setq custom-file (expand-file-name "custom.el" user-emacs-directory))
;; Ensure custom.el doesn't get loaded even if it exists
(when (file-exists-p custom-file)
  (delete-file custom-file))

(setq-default show-trailing-whitespace t)
(add-hook 'before-save-hook 'delete-trailing-whitespace)

;; do not ask follow link
(customize-set-variable 'find-file-visit-truename t)
(setq vc-follow-symlinks t)

(fset 'yes-or-no-p 'y-or-n-p)

(setq gc-cons-threshold 100000000)
(setq read-process-output-max (* 1024 1024))
(setq inhibit-compacting-font-caches t)

(unless (display-graphic-p)
  (set-face-background 'default "unspecified-bg"))

(setq initial-major-mode 'text-mode)

(setq locale-coding-system 'utf-8)
(set-terminal-coding-system 'utf-8)
(set-keyboard-coding-system 'utf-8)
(set-selection-coding-system 'utf-8)
(prefer-coding-system 'utf-8)

(setq create-lockfiles nil)
(setq inhibit-startup-message t)
(setq initial-scratch-message nil)
(setq-default indent-tabs-mode nil)
(setq-default tab-width 4)
(setq ring-bell-function 'ignore)

;; allow system clipboard
(setq select-enable-primary nil)
(setq select-enable-clipboard t)
(setq x-select-enable-primary nil)
(setq x-select-enable-clipboard t)
(setq xclip-select-enable-clipboard t)

(setq require-final-newline t)
(setq load-prefer-newer t)

(setq frame-inhibit-implied-resize t)
(setq fast-but-imprecise-scrolling t)
(setq redisplay-skip-fontification-on-input t)

(if (fboundp 'tool-bar-mode)
    (tool-bar-mode -1))
(if (fboundp 'set-scroll-bar-mode)
    (set-scroll-bar-mode nil))
(if (fboundp 'menu-bar-mode)
    (menu-bar-mode -1))
(setq use-dialog-box nil)
(setq use-file-dialog nil)

(fido-mode 1)
(fido-vertical-mode 1)

(repeat-mode 1)
(column-number-mode 1)
(size-indication-mode 1)
(global-visual-line-mode 1)
(show-paren-mode 1)
(delete-selection-mode 1)
(electric-pair-mode 1)
(setq pixel-scroll-precision-mode 1)

(setq mouse-drag-and-drop-region t
      mouse-drag-and-drop-region-cut-when-buffers-differ t
      mouse-drag-and-drop-region-show-tooltip t
      mouse-drag-and-drop-region-show-cursor t)

(unless (display-graphic-p)
  (require 'mouse)
  (xterm-mouse-mode 1)
  (global-set-key [mouse-4] (lambda () (interactive) (scroll-down 5)))
  (global-set-key [mouse-5] (lambda () (interactive) (scroll-up 5))))

;; Define the autosaves directory
(setq auto-save-file-name-transforms
      `((".*" ,(expand-file-name "autosaves/" user-emacs-directory) t)))

;; Ensure the directory exists
(let ((autosaves-dir (expand-file-name "autosaves/" user-emacs-directory)))
  (unless (file-directory-p autosaves-dir)
    (make-directory autosaves-dir t)))

(setq user-emacs-directory (expand-file-name "~/.emacs.d/"))
;; (setq custom-file (expand-file-name "custom.el" user-emacs-directory))

(setq backup-directory-alist `(("." . ,(expand-file-name "backups/" user-emacs-directory))))
(setq auto-save-file-name-transforms `((".*" ,(expand-file-name "autosaves/" user-emacs-directory) t)))
(setq auto-save-list-file-prefix (expand-file-name "auto-save-list/saves-" user-emacs-directory))
(setq url-history-file (expand-file-name "url/history" user-emacs-directory))

(setq bookmark-default-file (expand-file-name "bookmarks" user-emacs-directory))
(setq recentf-save-file (expand-file-name "recentf" user-emacs-directory))

(setq desktop-dirname (expand-file-name "desktop/" user-emacs-directory))
(setq desktop-path (list desktop-dirname))
(setq desktop-base-file-name "emacs.desktop")

(setq org-directory (expand-file-name "~/org/"))  ;; Typically set to ~/org for better organization
(setq tramp-persistency-file-name (expand-file-name "tramp" user-emacs-directory))

(setq uniquify-buffer-name-style 'forward
      uniquify-separator "/"
      uniquify-after-kill-buffer-p t
      uniquify-ignore-buffers-re "^\\*"
      uniquify-min-dir-content 1)

(recentf-mode 1)
(setq recentf-max-saved-items 100
      recentf-max-menu-items 15
      recentf-auto-cleanup 'never
      recentf-exclude '("/tmp/" "/ssh:")
      recentf-save-file (locate-user-emacs-file "recentf"))

(global-set-key (kbd "C-x C-r") 'recentf-open-files)

(savehist-mode 1)
(setq savehist-additional-variables '(search-ring regexp-search-ring))
(setq savehist-autosave-interval 60)

(require 'server)
(unless (server-running-p)
  (server-start))

(save-place-mode 1)
(setq save-place-file (locate-user-emacs-file "places"))

(setq isearch-allow-scroll t
      isearch-lazy-highlight t
      isearch-lazy-count t
      lazy-highlight-initial-delay 0
      lazy-highlight-cleanup t
      search-highlight t
      search-whitespace-regexp ".*?"
      case-fold-search t
      isearch-regexp-lax-whitespace nil
      isearch-lax-whitespace t)

(with-eval-after-load 'isearch
  (define-key isearch-mode-map [remap isearch-delete-char] 'isearch-del-char)
  (define-key isearch-mode-map (kbd "C-h") 'isearch-delete-char)

  (define-key isearch-mode-map (kbd "<return>") 'isearch-repeat-forward)
  (define-key isearch-mode-map (kbd "S-<return>") 'isearch-repeat-backward)

  (define-key isearch-mode-map (kbd "M-r") 'isearch-toggle-regexp)
  (define-key isearch-mode-map (kbd "M-s C-e") 'isearch-yank-line)
  (define-key isearch-mode-map (kbd "M-s w") 'isearch-yank-word)


  (add-hook 'isearch-mode-end-hook (lambda () (recenter))))

(setq dired-recursive-copies 'always
      dired-recursive-deletes 'always
      dired-dwim-target t
      delete-by-moving-to-trash t)

(put 'dired-find-alternate-file 'disabled nil)

(add-hook 'dired-mode-hook 'dired-hide-details-mode)

(setq dired-listing-switches "-alh --group-directories-first")

(setq ediff-window-setup-function 'ediff-setup-windows-plain
      ediff-split-window-function 'split-window-horizontally
      ediff-merge-split-window-function 'split-window-horizontally)
