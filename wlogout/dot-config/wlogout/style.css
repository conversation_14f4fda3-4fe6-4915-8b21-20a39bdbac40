* {
  font-family: "Fira Sans Semibold", FontAwesome, Roboto, Cascadia Code, Jetbrains Mono, Helvetica, Arial, sans-serif;
  background-image: none;
  box-shadow: none;
}

window {

  background-color: rgba(48, 52, 70, 0.90);
  background-size: cover;
  font-size: 16pt;
  color: #cdd6f4;
}

button {
  border-radius: 0;
  border-color: #ca9ee6;
  text-decoration-color: #c6d0f5;
  color: #c6d0f5;
  background-color: #292c3c;
  border-style: solid;
  border-width: 1px;
  background-repeat: no-repeat;
  background-position: center;
  background-size: 25%;

  animation: gradient_f 20s ease-in infinite;
  transition: all 0.3s cubic-bezier(.55, 0.0, .28, 1.682), box-shadow 0.2s ease-in-out, background-color 0.2s ease-in-out;
}

button:focus,
button:active,
button:hover {
  /* 20% Overlay 2, 80% mantle */
  background-color: rgb(63, 66, 85);
  outline-style: none;
}

#lock {
  background-image: url("icons/lock.svg");
}

#hibernate {
  background-image: url("icons/hibernate.svg");
}

#logout {
  background-image: url("icons/logout.svg");
}

#suspend {
  background-image: url("icons/suspend.svg");
}

#shutdown {
  background-image: url("icons/shutdown.svg");
}

#reboot {
  background-image: url("icons/reboot.svg");
}
