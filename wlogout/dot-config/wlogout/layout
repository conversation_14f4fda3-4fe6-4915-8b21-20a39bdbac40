{
  "label": "lock",
  "action": "hyprlock",
  "text": "Lock",
  "keybind": "l"
}
{
  "label": "hibernate",
  "action": "systemctl hibernate",
  "text": "Hibernate",
  "keybind": "h"
}
{
  "label": "logout",
  "action": "hyprctl dispatch exit",
  "text": "Log Out",
  "keybind": "e"
}
{
  "label": "suspend",
  "action": "systemctl suspend",
  "text": "Suspend",
  "keybind": "u"
}
{
  "label": "reboot",
  "action": "systemctl reboot",
  "text": "Restart",
  "keybind": "r"
}
{
  "label": "shutdown",
  "action": "systemctl poweroff",
  "text": "Power Off",
  "keybind": "s"
}
