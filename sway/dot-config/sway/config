xwayland disable


input "type:keyboard" {
	xkb_options caps:ctrl_modifier
}

output HDMI-A-1 resolution 3840x2160 position 0,0 scale 1.75
output HDMI-A-2 resolution 3840x2160 position 0,0 scale 1.75
output DP-1 resolution 3840x2160 position 0,0 scale 1.75
output DP-2 resolution 3840x2160 position 0,0 scale 1.75
output Virtual-1 resolution 3840x2160 position 0,0 scale 1.75

include cat<PERSON>uccin-mocha

set $mod Mod4

# Home row direction keys, like vim
set $left h
set $down j
set $up k
set $right l

set $term foot

set $rofi_cmd rofi -terminal '$term'

# set $menu $rofi_cmd -show combi -combi-modes drun#run -modes combi
set $menu $rofi_cmd -show drun
set $screenlock 'swaylock --config ~/.ilm/suseway/dot-config/swaylock.conf'
set $menu_window $rofi_cmd -show window
set $wob wob --config /etc/sway/wob/wob.ini

exec env.sh
exec dbus-update-activation-environment --systemd WAYLAND_DISPLAY XDG_CURRENT_DESKTOP
exec /usr/libexec/lxqt-policykit-agent
# exec /usr/libexec/polkit-gnome-authentication-agent-1

exec swaync
exec nm-applet
exec wl-paste -t text --watch clipman store --no-persist
# exec wl-paste --watch cliphist store

# output * bg ~/.config/wallpapers/wallpaper.png fill
exec_always swaybg -i ~/.config/wallpapers/wallpaper.png -m fill

exec swayidle -w \
        timeout 240 'swaylock -f' \
        timeout 300 'swaymsg "output * power off"' \
        resume 'swaymsg "output * power on"'  \
        timeout 180 'pgrep -xu "$USER" swaylock >/dev/null && swaymsg "output * power off"' \
        resume 'pgrep -xu "$USER" swaylock >/dev/null && swaymsg "output * power on"'  \
        before-sleep 'swaylock -f' \
        lock 'swaylock -f' \
        unlock 'pkill -xu "$USER" -SIGUSR1 swaylock' \
        timeout 600 'systemctl suspend'

exec_always {
    systemctl --user import-environment
    gsettings set org.gnome.desktop.interface gtk-theme 'Adwaita-dark'
    gsettings set org.gnome.desktop.interface icon-theme 'Adwaita'
    gsettings set org.gnome.desktop.interface cursor-theme 'Adwaita'
    gsettings set org.gnome.desktop.interface color-scheme 'prefer-dark'
    test -e $SWAYSOCK.wob || mkfifo $SWAYSOCK.wob
    tail -f $SWAYSOCK.wob | $wob
    swaync --style ~/.config/swaync/style.css --config ~/.config/swaync/config.json
}

default_border pixel 3
default_floating_border pixel 3

gaps inner 4
gaps outer 2

smart_borders on
# smart_gaps on

bindsym $mod+shift+g exec swaymsg gaps outer all set 0 && swaymsg gaps inner all set 5
bindsym $mod+g exec swaymsg gaps outer all set 0 && swaymsg gaps inner all set 0

# target                 title     bg    text   indicator  border
client.focused           $lavender $base $text  $rosewater $lavender
client.focused_inactive  $overlay0 $base $text  $rosewater $overlay0
client.unfocused         $overlay0 $base $text  $rosewater $overlay0
client.urgent            $peach    $base $peach $overlay0  $peach
client.placeholder       $overlay0 $base $text  $overlay0  $overlay0
client.background        $base

bar {
    swaybar_command waybar
}

bindsym $mod+Return exec $term
bindsym $mod+Shift+q kill
bindsym $mod+d exec $menu
bindsym $mod+shift+d exec $menu_window

floating_modifier $mod normal

# Reload the configuration file
bindsym $mod+Shift+c reload

# Exit sway (logs you out of your Wayland session)
bindsym $mod+Shift+e exec swaynag -t warning -m 'You pressed the exit shortcut. Do you really want to exit sway? This will end your Wayland session.' -B 'Yes, exit sway' 'swaymsg exit'
# bindsym $mod+Shift+e exec ~/.ilm/bin/rofi-logout

# Move your focus around
bindsym $mod+$left focus left
bindsym $mod+$down focus down
bindsym $mod+$up focus up
bindsym $mod+$right focus right
# Or use $mod+[up|down|left|right]
bindsym $mod+Left focus left
bindsym $mod+Down focus down
bindsym $mod+Up focus up
bindsym $mod+Right focus right

# Move the focused window with the same, but add Shift
bindsym $mod+Shift+$left move left
bindsym $mod+Shift+$down move down
bindsym $mod+Shift+$up move up
bindsym $mod+Shift+$right move right
# Ditto, with arrow keys
bindsym $mod+Shift+Left move left
bindsym $mod+Shift+Down move down
bindsym $mod+Shift+Up move up
bindsym $mod+Shift+Right move right
#
# Workspaces:
#
# Switch to workspace
bindsym $mod+1 workspace number 1
bindsym $mod+2 workspace number 2
bindsym $mod+3 workspace number 3
bindsym $mod+4 workspace number 4
bindsym $mod+5 workspace number 5
bindsym $mod+6 workspace number 6
bindsym $mod+7 workspace number 7
bindsym $mod+8 workspace number 8
bindsym $mod+9 workspace number 9
# bindsym $mod+0 workspace number 10
# Move focused container to workspace
bindsym $mod+Shift+1 move container to workspace number 1, workspace 1
bindsym $mod+Shift+2 move container to workspace number 2, workspace 2
bindsym $mod+Shift+3 move container to workspace number 3, workspace 3
bindsym $mod+Shift+4 move container to workspace number 4, workspace 4
bindsym $mod+Shift+5 move container to workspace number 5, workspace 5
bindsym $mod+Shift+6 move container to workspace number 6, workspace 6
bindsym $mod+Shift+7 move container to workspace number 7, workspace 7
bindsym $mod+Shift+8 move container to workspace number 8, workspace 8
bindsym $mod+Shift+9 move container to workspace number 9, workspace 9
# bindsym $mod+Shift+0 move container to workspace number 10, workspace 10

bindsym $mod+ctrl+left workspace next
bindsym $mod+ctrl+right workspace prev
bindsym $mod+shift+ctrl+left move container to workspace next, workspace next
bindsym $mod+shift+ctrl+right move container to workspace prev, workspace prev

bindsym $mod+b splith
bindsym $mod+v splitv

bindsym $mod+tab workspace next_on_output
bindsym $mod+Shift+tab workspace prev_on_output

# Switch the current container between different layout styles
bindsym $mod+s layout stacking
bindsym $mod+w layout tabbed
bindsym $mod+e layout toggle split

# Make the current focus fullscreen
bindsym $mod+f fullscreen

# Toggle the current focus between tiling and floating mode
bindsym $mod+Shift+space floating toggle

# Swap focus between the tiling area and the floating area
bindsym $mod+space focus mode_toggle

# Move focus to the parent container
bindsym $mod+a focus parent

# Move the currently focused window to the scratchpad
bindsym $mod+Shift+minus move scratchpad

# Show the next scratchpad window or hide the focused scratchpad window.
# If there are multiple scratchpad windows, this command cycles through them.
bindsym $mod+minus scratchpad show
#
# Resizing containers:
#
bindsym $mod+alt+$left resize shrink width 10px
bindsym $mod+alt+$down resize grow height 10px
bindsym $mod+alt+$up resize shrink height 10px
bindsym $mod+alt+$right resize grow width 10px
bindsym $mod+alt+Left resize shrink width 10px
bindsym $mod+alt+Down resize grow height 10px
bindsym $mod+alt+Up resize shrink height 10px
bindsym $mod+alt+Right resize grow width 10px

mode "resize" {
    bindsym $left resize shrink width 10px
    bindsym $down resize grow height 10px
    bindsym $up resize shrink height 10px
    bindsym $right resize grow width 10px

    # Ditto, with arrow keys
    bindsym Left resize shrink width 10px
    bindsym Down resize grow height 10px
    bindsym Up resize shrink height 10px
    bindsym Right resize grow width 10px

    # Return to default mode
    bindsym Return mode "default"
    bindsym Escape mode "default"
}
bindsym $mod+r mode "resize"

# Screenshots
set $selected_window swaymsg -t get_tree | jq -r '.. | select(.pid? and .visible?) | .rect | "\(.x),\(.y) \(.width)x\(.height)"' | slurp
set $focused_window swaymsg -t get_tree | jq -j '.. | select(.type?) | select(.focused).rect | "\(.x),\(.y) \(.width)x\(.height)"'
set $focused_output swaymsg -t get_outputs | jq -r '.[] | select(.focused) | .name'

## Screenshot commands
### Full
set $screenshot_full grim
set $screenshot_full_clipboard grim - | wl-copy
### Selected window
set $screenshot_selected_window $selected_window | grim -g-
set $screenshot_selected_window_clipboard $selected_window | grim -g- - | wl-copy
### Selected area
set $screenshot_selected_area slurp | grim -g-
set $screenshot_selected_area_clipboard slurp | grim -g- - | wl-copy
### Focused window
set $screenshot_focused_window $focused_window | grim -g-
set $screenshot_focused_window_clipboard $focused_window | grim -g- - | wl-copy
### Focused output
set $screenshot_focused_output grim -o $($focused_output)
set $screenshot_focused_output_clipboard grim -o $($focused_output) - | wl-copy

## Screenshot mode menu
set $screenshot "Screenshot: (f) full, (s) select window, (a) select area, (w) focused window, (o) focused output [Ctrl+ saves to clipboard]"
mode $screenshot {
    # Full
    bindsym f exec $screenshot_full; mode "default"
    bindsym Ctrl+f exec $screenshot_full_clipboard; mode "default"
    # Selected window
    bindsym s exec $screenshot_selected_window; mode "default"
    bindsym Ctrl+s exec $screenshot_selected_window_clipboard; mode "default"
    # Selected area
    bindsym a exec $screenshot_selected_area; mode "default"
    bindsym Ctrl+a exec $screenshot_selected_area_clipboard; mode "default"
    # Focused window
    bindsym w exec $screenshot_focused_window; mode "default"
    bindsym Ctrl+w exec $screenshot_focused_window_clipboard; mode "default"
    # Focused output
    bindsym o exec $screenshot_focused_output; mode "default"
    bindsym Ctrl+o exec $screenshot_focused_output_clipboard; mode "default"

    # Exit screenshot mode menu
    bindsym Return mode "default"
    bindsym Escape mode "default"
    bindsym $mod+Print mode "default"
}
bindsym $mod+Print mode $screenshot

bindsym $mod+0 mode "$mode_system"
set $mode_system (l)ock, (e)xit, switch_(u)ser, (s)uspend, (h)ibernate, (r)eboot, (Shift+s)hutdown
mode "$mode_system" {
    bindsym l exec swaylock, mode "default"
    bindsym s exec systemctl suspend, mode "default"
    bindsym u exec systemctl restart sddm, mode "default"
    bindsym e exec swaymsg exit, mode "default"
    bindsym h exec systemctl hibernate, mode "default"
    bindsym r exec systemctl reboot, mode "default"
    bindsym Shift+s exec systemctl poweroff, mode "default"
    bindsym Return mode "default"
    bindsym Escape mode "default"
}

set $ws1 "1"
set $ws2 "2"
set $ws3 "3"
set $ws4 "4"
set $ws5 "5"

# Open applications on specific workspaces
assign [class="Alacritty"] $ws1
assign [class="kitty"] $ws1
assign [class="WezTerm"] $ws1
assign [class="foot"] $ws1
assign [app_id="firefox"] $ws3
assign [class="Chromium"] $ws3
assign [app_id="code"] $ws2
assign [app_id="org.telegram.desktop"] $ws5

exec $term
exec firefox
exec code
exec flatpak run org.telegram.desktop

# Open specific applications in floating mode
for_window [title="alsamixer"] floating enable border pixel 1
for_window [class="Clipgrab"] floating enable
for_window [title="File Transfer*"] floating enable
for_window [class="Galculator"] floating enable border pixel 1
for_window [class="GParted"] floating enable border normal
for_window [title="i3_help"] floating enable sticky enable border normal
for_window [class="Lightdm-gtk-greeter-settings"] floating enable
for_window [class="Lxappearance"] floating enable sticky enable border normal
for_window [class="Manjaro-hello"] floating enable
for_window [class="Manjaro Settings Manager"] floating enable border normal
for_window [title="MuseScore: Play Panel"] floating enable
for_window [class="Nitrogen"] floating enable sticky enable border normal
for_window [class="Oblogout"] fullscreen enable
for_window [class="octopi"] floating enable
for_window [class="Pamac-manager"] floating enable
for_window [class="Pavucontrol"] floating enable
for_window [class="qt5ct"] floating enable sticky enable border normal
for_window [class="Qtconfig-qt4"] floating enable sticky enable border normal
for_window [class="Simple-scan"] floating enable border normal
for_window [class="(?i)System-config-printer.py"] floating enable border normal
for_window [class="Thus"] floating enable border normal
for_window [class="Timeset-gui"] floating enable border normal
for_window [class="(?i)virtualbox"] floating enable border normal
for_window [class="(?i)virtualbox"] fullscreen enable
for_window [class="Xfburn"] floating enable

for_window [app_id="org.mozilla.firefox"] opacity 0.98
for_window [app_id="code"] opacity 0.98

### Input configuration
#
# Example configuration:
#
#   input "2:14:SynPS/2_Synaptics_TouchPad" {
#       dwt enabled
#       tap enabled
#       natural_scroll enabled
#       middle_emulation enabled
#   }
#

for_window [app_id="(Calendar|Calculator|Power-statistics|Control-center)"] floating enable
for_window [app_id="org.gnome.(Nautilus|Weather)"] floating enable
for_window [app_id="nm-connection-editor"] floating enable
for_window [app_id="(pavucontrol|psensor)"] floating enable
for_window [app_id="evolution-alarm-notify"] floating enable
for_window [app_id="nemo"] floating enable
for_window [app_id="evolution" title="Compose Message"] floating enable
for_window [app_id="evolution" title="Re(.*)"] floating enable
for_window [app_id="evolution" title="Fwd(.*)"] floating enable
for_window [app_id="mpv"] floating enable
for_window [app_id="mpv"] resize set 960 540
for_window [app_id="mpv"] border pixel 0
for_window [app_id="firefox" title="Pushbullet"] floating enable
for_window [app_id="firefox" title="Firefox — Sharing Indicator"] floating enable
for_window [app_id="firefox" title="Picture-in-Picture"] floating enable
for_window [app_id="firefox" title="Pushbullet – Mozilla Firefox"] floating enable
for_window [app_id="firefox" title="About Mozilla Firefox"] floating enable

# Brave, chrome, chromium
for_window [title="Picture-in-picture"] floating enable

for_window [app_id="lxqt-policykit-agent"] {
    floating enable
    move position center
}

# Fedora Sway
exec fedora.sh

# include '$(/usr/libexec/sway/layered-include "/usr/share/sway/config.d/*.conf" "/etc/sway/config.d/*.conf" "${XDG_CONFIG_HOME:-$HOME/.config}/sway/config.d/*.conf")'
